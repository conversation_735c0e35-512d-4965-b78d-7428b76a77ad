/* Sign-in button text */
"Sign in" = "साइन इन करें";

/* Long form sign-in button text */
"Sign in with Google" = "Google के साथ साइन इन करें";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "ठीक";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "अभी नहीं";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "सेटिंग";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "खाते में साइन इन नहीं किया जा सका";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "आपके एडमिन के लिए ज़रूरी है कि आप यह खाता एक्सेस करने के लिए इस डिवाइस पर एक पासकोड सेट करें. कृपया पासकोड सेट करें और दोबारा कोशिश करें.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "डिवाइस आपके एडमिन के ज़रिए सेट की गई सुरक्षा नीति का अनुपालन नहीं करता है.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "क्या Device Policy ऐप्लिकेशन से कनेक्ट करना चाहते हैं?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "अपने संगठन डेटा की सुरक्षा के लिए, आपको लॉग-इन करने से पहले Device Policy ऐप्लिकेशन से कनेक्ट करना होगा.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "कनेक्ट करें";
