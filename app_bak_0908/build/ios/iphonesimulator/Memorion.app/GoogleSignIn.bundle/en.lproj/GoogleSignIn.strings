/* Sign-in button text */
"Sign in" = "Sign in";

/* Long form sign-in button text */
"Sign in with Google" = "Sign in with Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "OK";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Cancel";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Settings";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Unable to sign in to account";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Your administrator requires you to set a passcode on this device to access this account. Please set a passcode and try again.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "The device is not compliant with the security policy set by your administrator.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Connect with Device Policy App?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "In order to protect your organization's data, you must connect with the Device Policy app before logging in.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Connect";
