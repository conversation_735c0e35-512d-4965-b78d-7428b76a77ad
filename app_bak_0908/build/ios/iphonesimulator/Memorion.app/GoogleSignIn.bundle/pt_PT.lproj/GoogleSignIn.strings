/* Sign-in button text */
"Sign in" = "Iniciar sessão";

/* Long form sign-in button text */
"Sign in with Google" = "Iniciar sessão com o Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "OK";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Cancelar";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Definições";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Não é possível iniciar sessão na conta";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "O administrador requer a definição de um código secreto neste dispositivo para aceder a esta conta. Defina um código secreto e tente novamente.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "O dispositivo não está em conformidade com a política de segurança definida pelo seu administrador.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Pretende ligar-se à aplicação Device Policy?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Para proteger os dados da sua entidade, tem de se ligar à aplicação Device Policy antes de iniciar sessão.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Ligar";
