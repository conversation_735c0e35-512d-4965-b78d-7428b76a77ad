/* Sign-in button text */
"Sign in" = "Masuk";

/* Long form sign-in button text */
"Sign in with Google" = "Masuk dengan Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "Oke";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Batal";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Setelan";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Tidak dapat login ke akun";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Administrator mengharuskan Anda menyetel kode sandi di perangkat ini untuk mengakses akun ini. Setel kode sandi dan coba lagi.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "Perangkat ini tidak sesuai dengan kebijakan keamanan yang disetel oleh administrator.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Sambungkan dengan Aplikasi Device Policy?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Untuk melindungi data organisasi, Anda harus tersambung dengan aplikasi Device Policy sebelum login.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Sambungkan";
