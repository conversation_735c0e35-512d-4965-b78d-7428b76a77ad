/* Sign-in button text */
"Sign in" = "Accedi";

/* Long form sign-in button text */
"Sign in with Google" = "Accedi con Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "OK";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Annulla";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Impostazioni";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Impossibile accedere all'account";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "L'amministratore richiede l'impostazione di un passcode sul dispositivo per accedere a questo account. Imposta un passcode e riprova.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "Il dispositivo non è conforme alle norme di sicurezza stabilite dall'amministratore.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Vuoi collegarti all'app Device Policy?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Per proteggere i dati della tua organizzazione, devi collegarti all'app Device Policy prima di accedere.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Collega";
