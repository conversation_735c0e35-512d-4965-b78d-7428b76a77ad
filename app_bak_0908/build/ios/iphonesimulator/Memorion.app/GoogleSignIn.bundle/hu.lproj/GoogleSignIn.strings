/* Sign-in button text */
"Sign in" = "Bejelentkezés";

/* Long form sign-in button text */
"Sign in with Google" = "Bejelentkezés Google-fiókkal";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "OK";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Mégse";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Beállítások";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Nem sikerült bejelentkezni a fiókba";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Adminisztrátora biztonsági kód beállítását kéri ezen az eszközön a fiókhoz való hozzáféréshez. K<PERSON>rjük, áll<PERSON>tson be biztonsági kódo<PERSON>, majd pr<PERSON><PERSON><PERSON>.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "Az eszköz nem felel meg a rendszergazda által beállított biztonsági házirendnek.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Csatlakozik a Device Policy alkalmazáshoz?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "A szervezet adatainak védelme érdekében a bejelentkezés előtt csatlakoznia kell a Device Policy alkalmazáshoz.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Csatlakozás";
