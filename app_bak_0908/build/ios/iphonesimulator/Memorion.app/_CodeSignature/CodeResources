<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>AppAuthCore_Privacy.bundle/Info.plist</key>
		<data>
		JczfJi0L8j3hnUcpuLMVseUzD48=
		</data>
		<key>AppAuthCore_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		MCDRptixYfA+qALBX9b4uxq3rwo=
		</data>
		<key>AppFrameworkInfo.plist</key>
		<data>
		yboPnR77E7aQcC0Lb6ik8YR3vLA=
		</data>
		<key>Assets.car</key>
		<data>
		QAQf86xdUutn6j0d8mCTz81y3QI=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<data>
		a6vFG0AKtYfO9s9YC31+wDdvdEk=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<data>
		n2t8gsDpfE6XkhG31p7IQJRxTxU=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<data>
		ZVgM1+KwZcZnwhgaI0F7Bt1ba2c=
		</data>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<data>
		hMnf/VIyTGR2nRcoLS3JCfeGmDs=
		</data>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<data>
		MDrKFvFWroTb0+KEbQShBcoBvo4=
		</data>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<data>
		nFC1waP0YzYOchnqa85lPwrC73s=
		</data>
		<key>Config/Dev/GoogleService-Info-bak0809.plist</key>
		<data>
		TlbsXfuDTcVl5lSFRdhhJhPf43g=
		</data>
		<key>Config/Dev/GoogleService-Info.plist</key>
		<data>
		SfOb8LGwcLrLO7bQHqcIJcmBUvk=
		</data>
		<key>Config/Prod/GoogleService-Info-bak0809.plist</key>
		<data>
		TlbsXfuDTcVl5lSFRdhhJhPf43g=
		</data>
		<key>Config/Prod/GoogleService-Info.plist</key>
		<data>
		kS4mXJ+D1+Wx1s0qFKNBJfeWNzU=
		</data>
		<key>Custom.xcconfig</key>
		<data>
		wYKV8UM7oVFQz+Vhf3aHsvcgya8=
		</data>
		<key>DKImagePickerController.bundle/Assets.car</key>
		<data>
		avJJy7HCcQiMfKnKHeqqRtETgw8=
		</data>
		<key>DKImagePickerController.bundle/Base.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			4xGD8Rwvi93HvaqnO7+vDZWDMBM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/Info.plist</key>
		<data>
		Uz+yQv0mkoE32Eg9Ir/J/tj9mZc=
		</data>
		<key>DKImagePickerController.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YLYZhRSzZVHV+D7U/AQzGNwF8Ug=
		</data>
		<key>DKImagePickerController.bundle/ar.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hmgKVe/ELj2gURZGT+GZDwZaP7Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/da.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			D/EoHjvTwHAWEzVuC2G6vBsJnu4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/de.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aBIMF8wyDvY06aV2Bd4D1ivcRU4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/en.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9Q/DfMw3bDYmzyQL9Dp2TQkyUvM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/es.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NevVA8t5NN7C0rKXChImH4O3YfE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/fr.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6cJMOe8JrYF03L3W9F79fIYPtE8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/hu.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0eCEwKbFu38BHkg5r6N8402dkXs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/it.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TAlKCVsmH6cihPALHZhnFoQs5LE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/ja.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PIZnwQ5EdXn9Sde2m08gpbjHacs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/ko.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kJxeSwpa45LW72gQKW/iC9Jn4zU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/nb-NO.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GT/zie43E+HBEWl2kTAJRkDdE9s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/nl.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lAVKBUVMnJkxVl9jrus2i2k1oIk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/pt_BR.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			tUPVite8WGcESkhEVtVsN+8aEx8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/ru.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0rIAuu7v039vnJh99/NkEhlYpXI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/tr.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			558fSIGiBHUJCg2iZQvHWlivw1Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/ur.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6EYiiFsDKyAlJwepmIqOnx80kwA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/vi.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			001MdN+6p4Wj2GxyuQi9jI2OymI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/zh-Hans.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Qfv+Sv1HN+PP6w3kSD8UxfCs8m8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/zh-Hant.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xDMDoCCnErpChkw3aU6nZGMhrMw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKPhotoGallery.bundle/Assets.car</key>
		<data>
		1FeSWjoOdHVw45gjT3/pxHr4aAY=
		</data>
		<key>DKPhotoGallery.bundle/Base.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NA0j4SwR2AsLRfnHuNxL8Z1By44=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKPhotoGallery.bundle/Info.plist</key>
		<data>
		atRc41dAJXaYPYFDWsgEg0GYCFM=
		</data>
		<key>DKPhotoGallery.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YLYZhRSzZVHV+D7U/AQzGNwF8Ug=
		</data>
		<key>DKPhotoGallery.bundle/en.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lBygwcHp7X/0n1lfhAX4rhrabvk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKPhotoGallery.bundle/zh-Hans.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TC+NpTOWlgfdj3gzJygtTnl0V0o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FBLPromises_Privacy.bundle/Info.plist</key>
		<data>
		87m2bJaQ5499plciwkH/WwoGQa8=
		</data>
		<key>FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		ZajnvEs/MYRS3X4TPLAhBWi8mc4=
		</data>
		<key>FirebaseAuth_Privacy.bundle/Info.plist</key>
		<data>
		YAuEg6M0J9q7SXhfGEk0dXEkzWc=
		</data>
		<key>FirebaseAuth_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		8cQ/rSS7XR8ohTuSAR27mQb1wiU=
		</data>
		<key>FirebaseCoreExtension_Privacy.bundle/Info.plist</key>
		<data>
		Q5bE5atjXeRYzF6xMRiSLgp4a1Y=
		</data>
		<key>FirebaseCoreExtension_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6iBlSEWzrD6zouHx4NqeDhjKSGQ=
		</data>
		<key>FirebaseCoreInternal_Privacy.bundle/Info.plist</key>
		<data>
		12Qu0A+jIq1ZK4YlsQkl+vGcQQU=
		</data>
		<key>FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		ifoThrqbbqoLG4yjAruMQRaf0Dw=
		</data>
		<key>FirebaseCore_Privacy.bundle/Info.plist</key>
		<data>
		Cg4ZKmR1Heb3dZetVQ2eKUUHwSE=
		</data>
		<key>FirebaseCore_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		sa2OhFlqdCIyz9oV7fUdDKWzFL0=
		</data>
		<key>FirebaseInstallations_Privacy.bundle/Info.plist</key>
		<data>
		VdDOClxDw5KMo9W4FfkYbFk+6NA=
		</data>
		<key>FirebaseInstallations_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		WXQUJr75eMRgiVnLGyf8Gr3uLUU=
		</data>
		<key>FirebaseMessaging_Privacy.bundle/Info.plist</key>
		<data>
		12Z/HtNckUcIpFJoJZ+An6nbUJo=
		</data>
		<key>FirebaseMessaging_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		gwv2NsO/6s7ik6Px6eTTTvacWN4=
		</data>
		<key>Frameworks/App.framework/App</key>
		<data>
		Dn2AS6Vjzpww5FSaiufhc5KwUUg=
		</data>
		<key>Frameworks/App.framework/Info.plist</key>
		<data>
		XyxtD534mnbrR//42JUriUJtPUs=
		</data>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<data>
		UQ4voORqJvKPQW+R/81Pb1yL47Q=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<data>
		VM7xwCt0EnhmAlbTb66M4ExT2+s=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<data>
		si7M+U/YitvTNhLPbscmar8u8rU=
		</data>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<data>
		lOFhfAFvBLPnc8JjdRcpQGRmZKk=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<data>
		wuP4PyWjiRhBHGfufdzPtzI4cMY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NativeAssetsManifest.json</key>
		<data>
		re4p7E8rPLLsN+wzaPN/+AVpXTY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/device_assets/frame_lib.lua</key>
		<data>
		8ZUNqEIaQY/tUiNldeiiMP4GPtE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYBLACKITALIC.OTF</key>
		<data>
		DegyA3ma+nCuNO+eKiIxRBR+itg=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYBOLD.OTF</key>
		<data>
		EA57f9JPrLyk2zjm1NfgHHO4KBg=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYHEAVYITALIC.OTF</key>
		<data>
		CUxPX28U1ZZO/o7ONzrJGGdL1Jg=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYLIGHTITALIC.OTF</key>
		<data>
		G/IStgBrAhzrE/a9zzfSvgWWBng=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYMEDIUM.OTF</key>
		<data>
		pfXgjdOMrC+Ff7dRD36Sojmbb1w=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYREGULAR.OTF</key>
		<data>
		4Z3xmeeNKW5bZJHUgyp2dWUy7UM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYSEMIBOLDITALIC.OTF</key>
		<data>
		AU8Tzfi5SGF2BYImSP4/nMbGSOQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYTHINITALIC.OTF</key>
		<data>
		pUdn+cSyIMr50Moka7KeSErZjc4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/1.mov</key>
		<data>
		USUzecS5/mpGD+z2+WzBTe8YO9M=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/2.mov</key>
		<data>
		s1ZpEwmCY1OtLGqQodI0HziNRtU=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/3.mov</key>
		<data>
		hGfjdFFUgZ4rRWWGWM1kCnMk66E=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/4.mov</key>
		<data>
		mLU0wFpzB+n1Cu4c/jrt3ToLm5Y=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/5.mov</key>
		<data>
		F6oIbds5J33+1Y5uRdJR79UeIC4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/Logo%20Text%20White.png</key>
		<data>
		EHhie5a9Ln070Q+zBFUYx1/k1EE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/ai_magic.svg</key>
		<data>
		1zNTppEn+Dgy0kjbwY9DwaLImew=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/app_launcher_icon.png</key>
		<data>
		sjg6zkrjrihQ6sBzUb3tjjR1naU=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/app_launcher_icon_old.png</key>
		<data>
		Pob9skLKDBdOQzMDfDrdb+nI1SA=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/app_launcher_icon_v1.png</key>
		<data>
		9jficVwx7K4VwGcrw40QmNM3wnM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/app_launcher_icon_v2.png</key>
		<data>
		IjwMMgRIW9wiOyR9ZOjo0rr4JJw=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/apple-reminders-logo.png</key>
		<data>
		FUxCj887pynE8hjS4PU/99tN1P4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/apple_logo.png</key>
		<data>
		MV9WMJnhRncuv9zN+RAmWz0cJ2Y=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/background.png</key>
		<data>
		KrXBKS3IGd0ueEetpGHRf9yJYmE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/blob.png</key>
		<data>
		uspoGpd4kXCdHSoATPTxF13FQPA=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/calendar_logo.png</key>
		<data>
		tBN5c5P8JQD3ZIU3ORAb/TxKGcs=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/checkbox.svg</key>
		<data>
		gzfcWTnvEy1tBfhXNc5tgb6PR2E=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/clone.png</key>
		<data>
		8xwu3OMpkw/r9v8GmNa7nkRk25Q=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/email_logo.png</key>
		<data>
		DBxDDaf/hwH6xHeVUpQKIozRfS8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/emotional_feedback_1.png</key>
		<data>
		kyGpy41xfs2+R8pKk803awSjY0o=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/facebook_logo.png</key>
		<data>
		8+xF5j//G6Gm4PchwKiyacylwJk=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/google_logo.png</key>
		<data>
		k1imMv52UgbQdsGK85yBWz19xh8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/gradient_card.png</key>
		<data>
		16p96gLMwMuLZjFhs4f1yYJVAYc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/herologo.png</key>
		<data>
		CGt/VLouJQYJq+LDsPu8sjFf+6A=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/herologo_v1.png</key>
		<data>
		kFS2unMDR8FG1kSEdPB0vd4RZ3g=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/herologo_v3.png</key>
		<data>
		nfnneg6/txKAQz3WoT75YwUWe30=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/herologo_v4.png</key>
		<data>
		YZ6kTZaW7EowPisSxSF/pOrJBYQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/ic_chart.svg</key>
		<data>
		o7vjdIbIDN9/Hztjw3hg+WHNtyE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/ic_clone_chat.svg</key>
		<data>
		rAo8Rx0SLaWyqyYSTUb0hmoqtu0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/ic_clone_plus.svg</key>
		<data>
		SavCTc6HzU2ZirJXNXrZkYK6tm0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/ic_dollar.svg</key>
		<data>
		S7F9TN39SX2AI/Z6F7gcI3YjXPI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/ic_persona_profile.svg</key>
		<data>
		dN2fYfEO18O76F8jmBVqKsuTCHM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/ic_setting_persona.svg</key>
		<data>
		KJdcHHJnFbYZt4AjzHT2+lhiwzE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/imessage_logo.svg</key>
		<data>
		Hnp7kkSYXoP00sueOzplfTaKp/E=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/instagram_logo.png</key>
		<data>
		vbeWIm2o1vqxV2dApbNmqohArqs=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/instruction_1.png</key>
		<data>
		JcNyKJMswZS4+3WgxkANUsuZq5Y=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/instruction_2.png</key>
		<data>
		rjTxV5zlT7C/TVzwGOz/rvCSneM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/instruction_3.png</key>
		<data>
		iiaT+isdepK9hTED0czK/FtCISE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/link_icon.svg</key>
		<data>
		533rDhdeUlpiFKeCSWhqWnmMIls=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/linkedin_logo.png</key>
		<data>
		t6Sd0CSiVoCWd60ke3HlEz+zIjE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/logo_transparent.png</key>
		<data>
		yCyLyM9bR3QhkbnXR37nmV8g1pI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/logo_transparent_v1.png</key>
		<data>
		UpTRU9wM73FFWVyx6NKdOs+Olk4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/logo_transparent_v2.png</key>
		<data>
		5L01KnVVn19YhZoiYJANKACYx1s=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/new_background.png</key>
		<data>
		uxem3FDxhjOffFAnCohKBQrpovQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/notion_logo.png</key>
		<data>
		criMUnUuy4ScrKXHizCKnbGUleQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/omi-devkit-without-rope.png</key>
		<data>
		gy/W/ucnIZmCw7npny9ANWQikg0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/omi-glass.png</key>
		<data>
		HfumAlqak6E4+nzywlI/z1a4Q3k=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/omi-without-rope-turned-off.png</key>
		<data>
		QycoLjo4gPcXLM9wMa454JRi3Vs=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/omi-without-rope.png</key>
		<data>
		KJZJdVBD4pzu3oPq8Iydre2H2F0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-bg-1.jpg</key>
		<data>
		KebHgkaEdvX8aCrXgkRyrpIkFvk=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-bg-2.jpg</key>
		<data>
		I2u4Ei82RK0fU5Be3JHTIAKFTwQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-bg-3.jpg</key>
		<data>
		OL/tlkjWTIkLroMFxTYT3etNpsU=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-bg-4.jpg</key>
		<data>
		zBxeW9RzoNhs7YfOrJK5NfYfAAc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-bg-5-1.jpg</key>
		<data>
		ew3/qK08PK+8MatlUuaSdH6qei0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-bg-5-2.jpg</key>
		<data>
		7CX3eoBXCTWfAH93FMB872Myijc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-bg-6.jpg</key>
		<data>
		R5UGTP+Hq9nD6LDxRRqE4rjv0dM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-language-grey.png</key>
		<data>
		ZFy2ahzD6MtDDiCZ1twOn+nkNX0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-name-grey.png</key>
		<data>
		/KRjTKW1fzUmahL+0glA6obdzw0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-name-white.png</key>
		<data>
		W32H96KWs86KrRShzz2eVX59Zv4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-name.png</key>
		<data>
		30hr6RKv22eMrZJp52fOVQUZd5M=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-permissions.png</key>
		<data>
		FE2Qr7L3jjiPHK/MKGEQ/nBpItQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding.mp4</key>
		<data>
		o7JaiJWREVGqoWUzfN2qPcvr8bk=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/recording_green_circle_icon.png</key>
		<data>
		e9q5ywCs3hsVNEqCxBL3xfPSmr8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/slack_logo.png</key>
		<data>
		T+fvErgkVEDAYSxvzjytOXidEck=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/speaker_0_icon.png</key>
		<data>
		ueBtXJpJwA1IYyPAd7swgr3i42M=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/speaker_1_icon.png</key>
		<data>
		eLZodxWHwOmQ5OThKZWn626r9qc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/splash.png</key>
		<data>
		+3Z1R9EVtcpmnUEvO2mcem97oMg=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/splash_icon.png</key>
		<data>
		yCyLyM9bR3QhkbnXR37nmV8g1pI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/splash_icon_v1.png</key>
		<data>
		a/GDFttf6Vv+0ctWV/J47wlPSiY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/splash_icon_v2.png</key>
		<data>
		5L01KnVVn19YhZoiYJANKACYx1s=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/splash_v1.png</key>
		<data>
		+eoAyhHHRjzcO4I2g8M4I202kuE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/splash_v2.png</key>
		<data>
		5U9RD7bw0/rFz9jjXz18+DWbcE8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/stars.png</key>
		<data>
		s1DmhIY9rq+YkEiyHcsNWnp5xj8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/stripe_logo.svg</key>
		<data>
		Tf6mHqLs2/eJl4Pl1Ti2hW24Sm8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/telegram_logo.png</key>
		<data>
		XMZ2lx0Qsb7Vq9+TUPPhK7y7bwk=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/whatsapp_logo.png</key>
		<data>
		atlOLIls4CQU3KOKKJIzxrqdQTU=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/x_logo.png</key>
		<data>
		CqQYeNDOt3KS2fhHsvTiHWiEYME=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/x_logo_mini.png</key>
		<data>
		gCVgj4Jdzr/IBDLfybPbb0wnWuY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/images/youtube_logo.png</key>
		<data>
		8SkBeMaMVXDwwJ2dafPzlpWxD6c=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/lottie_animations/no_internet.json</key>
		<data>
		n4Ce10ehDMmc/s9yF20T+PMUZjc=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/lottie_animations/server_error.json</key>
		<data>
		d21nsECT9UqVzeAfc3XqQSA7PPY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/lottie_animations/wave.json</key>
		<data>
		L20InGMrCnyzmjlki6DdSKBeIH8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/pdfs/favicon.png</key>
		<data>
		YaZ3V+0+ASndzNpun6DfJV7pKWI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/silero_vad.onnx</key>
		<data>
		jULtexGoK0JQxAb2dU8yi9LX6XY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/silero_vad.v5.onnx</key>
		<data>
		5DACjCbdZ9dgi3YIhIrCyyXOtak=
		</data>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		/CUoTuPQqqdexfyOT9lpJhV+2MQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<data>
		d/HDCIMboLCfzG8D0k6cTv3TOCY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<data>
		jorcFvUynkI19ktSsDO00cAAxTI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/awesome_notifications/test/assets/images/test_image.png</key>
		<data>
		Cz0nsBokXQp1k+nfv9LGbJtLUgw=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_sound/assets/js/async_processor.js</key>
		<data>
		BAx1n78l/pIb3Mcwr+IfZnX3EBk=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_sound/assets/js/tau_web.js</key>
		<data>
		4pp7HzFFgeM1B8p13KCh59+XAzA=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_sound_web/howler/howler.js</key>
		<data>
		DyCnIBTxxx+5ch5nKQsTKJ22EFE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_sound_web/src/flutter_sound.js</key>
		<data>
		ytZoqzLoUOdRTHH7o7LbrD9+FUY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_sound_web/src/flutter_sound_player.js</key>
		<data>
		aa6EkNRxYdqjnxQeKQ5h+QmZxRs=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_sound_web/src/flutter_sound_recorder.js</key>
		<data>
		cE5oYO0M2qnKYkJt9nZQfBeUa+I=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_sound_web/src/flutter_sound_stream_processor.js</key>
		<data>
		6+pRiFZLKf9YUZgO9gX6kevJ8eA=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf</key>
		<data>
		7teGwHCZ+RaeOlmcHre5BoeqKQI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf</key>
		<data>
		81quIJEuBhQeJAeT49COjDp9V+8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf</key>
		<data>
		P0CZcIU8VoKS4eqgHZLsdx4tErE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/frame_sdk/assets/frameLib.lua</key>
		<data>
		RbqBXx3ikWVRMdpJeRzyf+/JcSQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/google_sign_in_all_platforms_desktop/assets/post_auth_page.html</key>
		<data>
		lM+yfkj8KSgc8KIDHyKx8HWboZ0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/amap.svg</key>
		<data>
		/UcCcm5+vMY4bTjwHR+6ZAK7XtY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/apple.svg</key>
		<data>
		fbXH1s+tBcmpq6YuVXwbvrjCjr4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/baidu.svg</key>
		<data>
		kpvWwpzObQC6wQufGS+PiTcPx3U=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/citymapper.svg</key>
		<data>
		yf0tN4vpTm17f8jbPeJ+jFyF+xg=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/copilot.svg</key>
		<data>
		1JEvxX76Bj0Uk8DgYE36Xsmoypo=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/doubleGis.svg</key>
		<data>
		fHQQTkXBPXOa1OhYgPJ5rFn84ts=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/flitsmeister.svg</key>
		<data>
		Iz1bAUUX6f7pLtTmF+qKL8GGv48=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/google.svg</key>
		<data>
		WA9cr3Lw3dNrPylu/Y4wapia0Ak=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/googleGo.svg</key>
		<data>
		WA9cr3Lw3dNrPylu/Y4wapia0Ak=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/here.svg</key>
		<data>
		R2j1HvxQ3E/n4KLC/RAUjHp7iZY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/kakao.svg</key>
		<data>
		M++3cj4dldJ0Oe9bDdqxbDyPKuY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/mappls.svg</key>
		<data>
		r48JqBHo2eSvGqbAd0aUfc0ZSxI=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/mapswithme.svg</key>
		<data>
		2EG3vyWdMxu+oqseoMOQkPby5Wg=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/mapyCz.svg</key>
		<data>
		nMyMqj+C5a1uR8NyD/zGVGiQVr4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/naver.svg</key>
		<data>
		eBdoEUiRDCgPrN6WcOL1SZ9Lfro=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/osmand.svg</key>
		<data>
		xyb4z9N5g2wqyDoTR3pcLPI8960=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/osmandplus.svg</key>
		<data>
		T9sg0GPVvxFD1Ssi+hokqf/VBxU=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/petal.svg</key>
		<data>
		YdmQyjguDckOzTv92GPqLr/JRsg=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/sygicTruck.svg</key>
		<data>
		5pRnNG1kmOjpVDuNk980JNk93x4=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/tencent.svg</key>
		<data>
		xHB+4uLkTuaVH2EnJXsIGyTM+6k=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/tmap.svg</key>
		<data>
		oykNsjMlGfm/5sdClVP6+GiTRj8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/tomtomgo.svg</key>
		<data>
		mIFqWrF9ScJtuJ6V8Owkj6vNcvw=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/tomtomgofleet.svg</key>
		<data>
		Ey1jeyMheA7oYJnXdk2W9wkasYw=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/truckmeister.svg</key>
		<data>
		begso2B4oJ4hP9IYBzeKB/hr4g8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/waze.svg</key>
		<data>
		SOFZ9KgocLC41twkJv/jdJImYMk=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/yandexMaps.svg</key>
		<data>
		nP3c3vxkt2pPGxI76FH+HaYzARk=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/yandexNavi.svg</key>
		<data>
		Jgt5gGSCQD7dmm/g6KacQzee8k8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/mcumgr_flutter/assets/mock_logs.txt</key>
		<data>
		lVWuMm3Zjxf5MKkhot4H00KBC/U=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/mixpanel_flutter/assets/mixpanel.js</key>
		<data>
		vq0MWP2uYPXwXeO81KTtMyqQUI0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/opus_flutter_web/assets/libopus.js</key>
		<data>
		sAjtQOj3y5130cpkKmzgXWciCOo=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/opus_flutter_web/assets/libopus.wasm</key>
		<data>
		X+mi0qqbFHpSxaB+oFrH60mJ9+0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/opus_flutter_windows/assets/libopus_x64.dll.blob</key>
		<data>
		PQuahZP/PzgrJSx9ZZ/JSS/k3G8=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/opus_flutter_windows/assets/libopus_x86.dll.blob</key>
		<data>
		mj6Q4thG0D9nzKabgZ1FgkymE34=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/opus_flutter_windows/assets/opus_license.txt</key>
		<data>
		1L19/Dc7AbkQgRyvT78S2qMQF7w=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/win_ble/assets/BLEServer.exe</key>
		<data>
		SvVce5wyGWi0Eu8NNGAyGnkF5t0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/window_manager/images/ic_chrome_close.png</key>
		<data>
		1U2XC/VL4Uf0UjDVrfQmXY5r44E=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/window_manager/images/ic_chrome_maximize.png</key>
		<data>
		yjgxKji/xqzO7SSWHKS3+DF37rM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/window_manager/images/ic_chrome_minimize.png</key>
		<data>
		KCVbcXNIJ5yFbLd8YyKBpH54ltE=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/window_manager/images/ic_chrome_unmaximize.png</key>
		<data>
		JDI0MPNTceGjL+6TDSt8B19xS84=
		</data>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		VvTF10G1gIeea4aI0DhJjCjHgXQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/shorebird.yaml</key>
		<data>
		27wHOUUVLr7A3vgmP//4xMWe+7Q=
		</data>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<data>
		SoHnDHqzNKozN7o/zhsvOoXVErY=
		</data>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<data>
		WNP+BStfipzSWdoeUhLFML92WzM=
		</data>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<data>
		wTPJHICwW6wxY3b87ek7ITN5kJk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<data>
		zbvYFr9dywry0lMMrHuNOOaNgkY=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<data>
		ksjIMu5IPw+Q3rw2YkAx0KjxkdM=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<data>
		V/wkSSsyYdMoexF6wPrC3KgkL4g=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<data>
		vFsZXNqjflvqKqAzsIptQaTSJho=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<data>
		sUgX1PJzkvyinL5i7nS1ro/Kd5o=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<data>
		SpNs7IhIC7xP34Ej+LQCaEZkqik=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<data>
		AqVvCbPmgWMQKrRnib05Okrjbp0=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<data>
		bkw+DmHReHDg1PPcvmSjuLZrheA=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<data>
		UqnnVWwQEYYX56eu7lt6dpR3LIc=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<data>
		VjAwScWkWWSrDeetip3K4yhuwDU=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<data>
		crQ9782ULebLQfIR+MbBkjB7d+k=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<data>
		ocQVSiAiUMYfVtZIn48LpYTJA5w=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<data>
		EARXud6pHb7ZYP8eXPDnluMqcXk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<data>
		qWHw5VIWEa0NmJ1PMhD16nlfRKk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<data>
		31prWLso2k5PfMMSbf5hGl+VE6Y=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<data>
		LDr6kSVbUfyQFAxLwCACF5S2VEA=
		</data>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<data>
		76iDFTrqMb9ZkyISriH514A2cU4=
		</data>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<data>
		wJV5dCKEGl+FAtDc8wJJh/fvKXs=
		</data>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<data>
		D+cqXttvC7E/uziGjFdqFabWd7A=
		</data>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<data>
		1SeT/qlsPhqPCzhoWKySvlMA2c8=
		</data>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<data>
		ipm8hg7aB3LzsfShJfpNR0QQ4hw=
		</data>
		<key>Frameworks/Instabug.framework/Info.plist</key>
		<data>
		13wbFmWTMtbCpYHxu1Ou1vmEKfM=
		</data>
		<key>Frameworks/Instabug.framework/Instabug</key>
		<data>
		8S53nOuperLxHDqk7Ty2Kl+MZRE=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/Assets.car</key>
		<data>
		yNxLEDaNhgC6Fs5UFW9DZQjRRPg=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/Config.plist</key>
		<data>
		zmV6UqBSo6r1NOz798vd5O4zTBA=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/FID.js</key>
		<data>
		z4jnPaq0vybyhdKOJHGNOhsfjOI=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGActionSheetCell.nib</key>
		<data>
		28sb+YQa/IPAucVC0/5EhX0C3yU=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGActionSheetVC-iPhone.nib</key>
		<data>
		ROk78ne0CoWErdr4DqIblXFBgCs=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGBugVC-iPhone.nib</key>
		<data>
		BRTF/vr48N6G/Yjy71Fn2t9WvwI=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGChatCell.nib</key>
		<data>
		WVeMKpeYhkGI12NHPasDHWKaoHc=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGChatListVC-iPhone.nib</key>
		<data>
		pF3XyuGTCgJGMDLibTcL2E/mgCo=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGChatVC-iPhone.nib</key>
		<data>
		/GiKM60W5tqdFUqpcBmtMNLWmqM=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGFullScreenImageViewController.nib</key>
		<data>
		8GJaXZ+I7Lmf5RO+zSm9plZ2JUI=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGPoweredByView.nib</key>
		<data>
		+DD2unnopUvmqWJed3GdjNuuNGs=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGPromptCell.nib</key>
		<data>
		nqKBFtxE1hjUh3sh0N6s2i0SS7c=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGReplyView-iPhone.nib</key>
		<data>
		cSRWm2Iq5luooHMeWKaRdK9Pgv8=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGReportCategoryCell.nib</key>
		<data>
		Fob6Su5czfC8QJh+4TgZQkNZHMc=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGScreenshotVC-iPhone.nib</key>
		<data>
		9pRZEj/o9lwzhptHN/Zn+9jRkR4=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGVoiceNoteRecordingViewController-iPhone.nib</key>
		<data>
		mlR6U8rfPFOZmkYgKGjE7ylpyRI=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/Info.plist</key>
		<data>
		OG9x5eQiZbLa8lpygwjp1zadjmc=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugDaignosticsDataModel.momd/InstabugDaignosticsDataModel.mom</key>
		<data>
		c2pR/gLEza/sKbyK2rtbn1BK8xk=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugDaignosticsDataModel.momd/VersionInfo.plist</key>
		<data>
		il5a0h2jo6O/snHjiqgp8oL2lRw=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugDataModel.momd/InstabugDataModel.mom</key>
		<data>
		9Zr8RSPZT5ysMog+P4oUP6kArL0=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugDataModel.momd/InstabugDataModel.omo</key>
		<data>
		T2K7DIGI0VFPh+z+Yj/nT9aL3m0=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugDataModel.momd/VersionInfo.plist</key>
		<data>
		ltfiFuSHIPLDjz4MsmYBFAHJ8Cw=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugSurveysDataModel.momd/InstabugSurveysDataModel.mom</key>
		<data>
		oBkPjmoJJxDcBQQIbV+9F3eCbaA=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugSurveysDataModel.momd/VersionInfo.plist</key>
		<data>
		cIwOQKk81m/mJaP7KNNNIp68dI0=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugUserStepsDataModel.momd/UserStepsDataModel.mom</key>
		<data>
		ZHgm6fw3OEMg8Q7jjmW90gmPyd4=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugUserStepsDataModel.momd/VersionInfo.plist</key>
		<data>
		gJ90TbeGmu3KzpSL0SA5AtqMP/I=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/Instabug_dsym_upload.sh</key>
		<data>
		DP8KwyTvy5H7hSnVlrDff4kEb/s=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		qIzgfWEjw69oe/+vqj0TUajvbnM=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/UserAttributesDataModel.momd/UserAttributesDataModel.mom</key>
		<data>
		/vX96ROZc8342A2HBhNr391nb7Y=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/UserAttributesDataModel.momd/VersionInfo.plist</key>
		<data>
		+BLJJZUtOfRJP/ZD/ItEEfdmEu8=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		ue7/LrxP4slMNwJ0EBikWyv9L4s=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		3aDR1cKoCJk9SfJDbNwph9b8LUw=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/_CodeSignature/CodeResources</key>
		<data>
		12nOT792xjkCGM4V2+NckU1wsdY=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/ar.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WGWKsm0lr0i+xCHI3iI6qgGQUGc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/az.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			I5I2sMxFN6VpkLlxo2awAMDkpQc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/ca-ES.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			n1t9rsKSA/Qggp8b4YDmuYfgBUE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/ca.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			atokjgvUP1Vmk9gdy1FVd/0SWOw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/cs.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7vpjV0K1NCsK1triQSGsKuyHQYo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/da.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kdTabL7H2HnMKbiU1Ypr9YWn+SU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OmO6yBOPEjC40Ns1cyLfJqMHnRc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			E7vzp81spzp/OVcH3QMTbzZd9m0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/en.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			JT8QT5SebrK2XFFs3Iov80e9WkI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			2SNBjq0z1PBctZSiG95PJo1r4rA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/fi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XvkNwTMy/L9Ul2FJuIFsdXb6tn0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nb8AUSeVgi8iZZJtCZTM9Wnd1dY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/hu.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hu6dFQI5ARjgSZLw1ijXAuO1hJU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			b11yeT46Xu01huDf78GnDPTHFbQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6H7j42CvSdaI1R1Zej6RWtwMkto=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			I1wNK6mkWxnD8BPtLLbdQKyDyr0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/nb.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3nEYtISxR6bmu97Op52Os+biWYM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9Ul1WgexQcR1U8skkMoEbV+qsTc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/pl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/89hLMCQZUJU4t5D6d4omWX5cGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/pt-BR.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jPZtLy0k6hzRSC/2U3af6/kD1HE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/pt-PT.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vv2UhgZvnDlpsIXk/6Tm4jcTGE4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/ro.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3DHKaLWL+U6mJr3ZtkrZsMckbMg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vPyLdPpxI/agU4aWWK8NDs9eb70=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/sk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			JfWK424Bgf9OTJfyFNbMKu3/pGo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/strip-frameworks.sh</key>
		<data>
		YOS7aGxjvvRtsNAQCXrKoe5nT7w=
		</data>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/sv.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			46AlVm227dP6G6m6puFihDwfcZ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/tr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			leqXZMjnJYU4eKtP8ezzA3gDLqs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WBqtayk6qOeHjZtkPmpf7BQPMyQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/zh-Hant-TW.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vdr/3isGcsjy1OxQgNV5QWcDKNs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/4eBCWIJ9N0zXhm4ucKfovpyFUM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/_CodeSignature/CodeResources</key>
		<data>
		x72NjhXDAU7OKFGEMtWo7swMZgY=
		</data>
		<key>Frameworks/Instabug.framework/strip-frameworks.sh</key>
		<data>
		YOS7aGxjvvRtsNAQCXrKoe5nT7w=
		</data>
		<key>Frameworks/Intercom.framework/Assets.car</key>
		<data>
		jxjXs/AzM6yWGtqsfNXHxZPDExo=
		</data>
		<key>Frameworks/Intercom.framework/Info.plist</key>
		<data>
		MVobTlQFYdZ+JhAcr4tLCwEMMWM=
		</data>
		<key>Frameworks/Intercom.framework/InterBlocksAssets.bundle/Assets.car</key>
		<data>
		B/P+htJ7XsrE2XIH9uCZKWyxhRE=
		</data>
		<key>Frameworks/Intercom.framework/InterBlocksAssets.bundle/Info.plist</key>
		<data>
		TRA6MWBapnNAjHCHcmWP156Yz+M=
		</data>
		<key>Frameworks/Intercom.framework/Intercom</key>
		<data>
		kCeRjcm7c19l2UMSuRdKHEn61OY=
		</data>
		<key>Frameworks/Intercom.framework/Intercom.bundle/data/intercom_area_codes.json</key>
		<data>
		54e9anVjesbex2A5LAp2JmjGyng=
		</data>
		<key>Frameworks/Intercom.framework/Intercom.bundle/sound/admin_reply_delivered.caf</key>
		<data>
		XMWy6g3s8AfIbdY7c442SjDOgtM=
		</data>
		<key>Frameworks/Intercom.framework/Intercom.bundle/sound/message_failed.caf</key>
		<data>
		JQZrxGFCKG3J3gaOIFdYE3aZSoU=
		</data>
		<key>Frameworks/Intercom.framework/Intercom.bundle/sound/message_operator.caf</key>
		<data>
		ojoAPvdAStxPZQ1m0b1gcklq+Ho=
		</data>
		<key>Frameworks/Intercom.framework/Intercom.bundle/sound/message_sending.caf</key>
		<data>
		4ex9cI824foKdX7c25IY893/pio=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/Info.plist</key>
		<data>
		8/U23K6kqQaA55CHacA4f7r65rA=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/ar.strings</key>
		<data>
		/Qi5wshnuxWYuvl62xR3IQdIJbk=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/bg.strings</key>
		<data>
		IN58sEmlL2EMp4nVPC+YxNzYJ/o=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/bn.strings</key>
		<data>
		+n3/IIj6Rbodbw83GFxQGBZr6co=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/bs.strings</key>
		<data>
		qw3NpTW+oCzuTL+d/kzNrj5sjEs=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/ca.strings</key>
		<data>
		eK/aAqlsKk8RKTo4iCmkpARW6z0=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/cs.strings</key>
		<data>
		7Rcvyo/zZlwkOxGtgfbE65EeHC4=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/da.strings</key>
		<data>
		8+0Kt+y0FE7+dUXcJnSdu/XieA8=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/de-form.strings</key>
		<data>
		GgtfV8PN3ItAPwWSmhkKmKQfChw=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/de.strings</key>
		<data>
		z6KiG7nbpMf2Wp0ztnTJ36AJlEQ=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/el.strings</key>
		<data>
		+jbhsCGnVGk8jTjS3O3zeIrv+Ag=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/en.strings</key>
		<data>
		TRt8fdCws781nt44Vtf75LfoGhw=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/es.strings</key>
		<data>
		wJHPc2uzgBbOSQk3F/5zRxEJXuc=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/et.strings</key>
		<data>
		Vn70MiOeb05zEMq/U0ZVwMWtbfk=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/fa-ir.strings</key>
		<data>
		FapFU4vnwbAkpqG1o9tfhycj57g=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/fi.strings</key>
		<data>
		50/bRVgreTdD5ANpcrEMUAQgLOY=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/fr.strings</key>
		<data>
		JP88DbrVUh2SrmPWzyPpLgBaBSk=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/he.strings</key>
		<data>
		9sOzQW5tnREgPy8V3s9HKuIm1o4=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/hi.strings</key>
		<data>
		NQBkSmhRM+1G5pdq2BObJZsH5Vw=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/hr.strings</key>
		<data>
		NKfm8NlQEISj/aeO+gc9Rv6NcW0=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/hu.strings</key>
		<data>
		tR1u+9nsuEO8A83vIKie18qThhU=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/id.strings</key>
		<data>
		YqrfmQobnaA4wOiLHCTNZBHGWf8=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/it.strings</key>
		<data>
		CuplAS9+WlTi2tqetZP06x5Mee4=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/ja.strings</key>
		<data>
		tQ/+sPxMNGgRRT4SK5Me3VfzlBM=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/ko.strings</key>
		<data>
		mhi1kYSMhA4ayb8KdgJPon54CKM=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/lt.strings</key>
		<data>
		kGzk+MObR0/aA6opX1TD/uVwyhw=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/lv.strings</key>
		<data>
		UPPnadFeFBLc00kcB+8taxYlaWg=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/mn.strings</key>
		<data>
		pSDzQcuXgmFUJkpkxwEu4S9qqak=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/ms.strings</key>
		<data>
		iMQ/qAerq0VMl9GUGa0MVxdH83M=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/nb.strings</key>
		<data>
		EiKgWJzVMgY7poOgQNKzK//G4Gc=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/nl.strings</key>
		<data>
		jhYwQKwgJSGWsuvuxwtMFYoBuKI=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/pl.strings</key>
		<data>
		eZ8qJqh+fsthvDzRyqVTEIY+ji0=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/pt-br.strings</key>
		<data>
		SVtyEcsqUapvd8XHucxQe0G+bK4=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/pt-pt.strings</key>
		<data>
		4xPFi/aBhuazPwmBQ+2eZx/MYto=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/ro.strings</key>
		<data>
		6iTNNN5VebgNea2x+nrDwQC3XlA=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/ru.strings</key>
		<data>
		4p/xnw2RaRHy7LDfKOf+eDSkMFk=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/sk.strings</key>
		<data>
		WrMcnyVxCdZBQ/5v/NDVhkRs5L0=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/sl.strings</key>
		<data>
		8y/pT5DyK9104HBUUmyboqsnLB0=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/sr.strings</key>
		<data>
		LOHPTW2A9ddEdJMs5ImVpsG2j78=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/sv.strings</key>
		<data>
		+azWBummoJgRL8QDxNryb0+8pK8=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/sw.strings</key>
		<data>
		16Qj+RF7D0ohcH+Q7/Cp/dpTxrQ=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/th.strings</key>
		<data>
		BpyqC9WyO/Ye2nwq6SmhYROhXBY=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/tr.strings</key>
		<data>
		5iFTruCV3S5Vx+viYRpSF8t5x1o=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/uk.strings</key>
		<data>
		qIvjNfHZJzRIgVJTOUUV84qAdag=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/vi.strings</key>
		<data>
		w7bjWThxKykyb3izKHoEp77hVPg=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/zh-hans.strings</key>
		<data>
		bWciyzpWO0lNptQXtIEIoUTvaiw=
		</data>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/zh-hant.strings</key>
		<data>
		CRmyR42w5wGb2rxf/xWDOpEBqfc=
		</data>
		<key>Frameworks/Intercom.framework/PINCache.bundle/Info.plist</key>
		<data>
		9iGYWE6kDWB3xDwJkFdRGKhQA/M=
		</data>
		<key>Frameworks/Intercom.framework/PINCache.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		8s3cq/vtcqYLKrB/zFQJQ7yKXGQ=
		</data>
		<key>Frameworks/Intercom.framework/PINOperation.bundle/Info.plist</key>
		<data>
		eFlHbPfd56MpyZ3RpHaVAhsmuDo=
		</data>
		<key>Frameworks/Intercom.framework/PINOperation.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		ucg9pita0v8d353x3NuGfxweQYU=
		</data>
		<key>Frameworks/Intercom.framework/PINRemoteImage.bundle/Info.plist</key>
		<data>
		yA0xGzv49IATFI0qKRvcNOumpeU=
		</data>
		<key>Frameworks/Intercom.framework/PINRemoteImage.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		U/PCcbKKMHXPIK1B37eZBDZYsr8=
		</data>
		<key>Frameworks/Intercom.framework/PrivacyInfo.xcprivacy</key>
		<data>
		SPx0A639arALHWcGRl7W8ZqxfYw=
		</data>
		<key>Frameworks/Intercom.framework/_CodeSignature/CodeResources</key>
		<data>
		texW5/7+ox9JmvP8zc2858c4j60=
		</data>
		<key>Frameworks/OpusKit.framework/Info.plist</key>
		<data>
		SO7lXBpoeGXz654wREKI/WJkM8Q=
		</data>
		<key>Frameworks/OpusKit.framework/OpusKit</key>
		<data>
		NgFcKLOJwbdaRCwVmpFjUcAU5o0=
		</data>
		<key>Frameworks/OpusKit.framework/_CodeSignature/CodeResources</key>
		<data>
		tdRozvaxUvmoqigRkAI+4npvoKQ=
		</data>
		<key>GTMSessionFetcher_Core_Privacy.bundle/Info.plist</key>
		<data>
		P1eGX2thxP7ij3YU4KLh4MWNb3U=
		</data>
		<key>GTMSessionFetcher_Core_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		GqeAMkwbcNQeG0K4qQhQh2vHhHo=
		</data>
		<key>GoogleDataTransport_Privacy.bundle/Info.plist</key>
		<data>
		+Uv3Mxsszaghvh9z3Ys1DfAPFSk=
		</data>
		<key>GoogleDataTransport_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		WXQUJr75eMRgiVnLGyf8Gr3uLUU=
		</data>
		<key>GoogleService-Info.plist</key>
		<data>
		BNYIbd8fkqpgOYoIxB33b+WGtNU=
		</data>
		<key>GoogleSignIn.bundle/Info.plist</key>
		<data>
		YrSnIqJU1bnC1ODN5rBTS70I7Qs=
		</data>
		<key>GoogleSignIn.bundle/Roboto-Bold.ttf</key>
		<data>
		RzJ98PNefNfIZFh0iXp0SWl1RK4=
		</data>
		<key>GoogleSignIn.bundle/ar.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1U4RM14vSknB5uPRHwbBf2P3L9k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/ca.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GWFStbBzSsozR8ndnlnCGWrVgIE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/cs.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rkKd2a1WbhspF12uj7jbNaxdcsw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/da.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CH8u4ji0ph6g0lWmwFmjuWeHjcQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/de.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			h9KGBhz6GBwN3U8va+ZhDVBbEsk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/el.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Sa1DzFvme+rejqFjRGh0R8Wvi2U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/en.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			abhi3BU5/ZWaUMKlntWlHWqGpfk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/en_GB.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iMO9o55MsPkZZzMfqxaQRohEIEs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/es.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			M6+vCrtORDxDSYRfCs0EQa/NF7E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/es_MX.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rOsbQGNRZKkOBW4HhfQBTWwEQyI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/fi.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gWfyNCqhd30Y2+42oIuL/HWmczg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/fr.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uazilWliTrW6MuAL6umfC/8JA2w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/fr_CA.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Esz3mlwnuaEJRGq9Kux4r37SBuQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/google.png</key>
		<data>
		aRs/zX73YrSGLLTP8EMEDJQGeTs=
		</data>
		<key>GoogleSignIn.bundle/<EMAIL></key>
		<data>
		xu0SOLMY2id0HRzRihn0qsuFhAc=
		</data>
		<key>GoogleSignIn.bundle/<EMAIL></key>
		<data>
		xbEbn7T4aiYsnIp0S2SVilad3d0=
		</data>
		<key>GoogleSignIn.bundle/he.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ui2oaT67k0R8hevxOaYRPvkBncY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/hi.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kZvr7XJerhHMM69PkXbnvBFp03o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/hr.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			f1AChdyqugygMHLDk8gT3Dci+JI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/hu.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BSoNTBIf4kWxwJJhk3j4LqTytTQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/id.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Frdz7EAjoTfEGLrt45QOxezjzXY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/it.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ip22TOlHApPdL0R4Op+px9lnPt4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/ja.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			sX0ftdSQz9WqdXlSCYZn0tktc/0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/ko.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			MXfc9JCBsSmgxmJ3xfMpVntvLfE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/ms.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ZjmLydbxaQLSdIBGmX5LQQXMfhQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/nb.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8kbG/vzCsprxXEBLWQPjsZYsmSM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/nl.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/CN7CHAVyaosf4bXVc5GxzC2Zbc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/pl.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IBrsi/T3mBKZccejeg+eKu+eLC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/pt.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			a2Crth0x+hSdaEGM4YAiNqZxYKg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/pt_BR.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			a2Crth0x+hSdaEGM4YAiNqZxYKg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/pt_PT.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			d90ZsiztKcH6gT0M2GNhS9budLg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/ro.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			wH6Vf08sJ8KKd++qPXetaXtSSEg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/ru.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dzPNIKMH551ZsL1tfx25Qj7g2fs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/sk.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ltpfHSCCPpO3bQBP56jjt96+d90=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/sv.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6zdaseAWNL3CgzM6rhievkeYLBY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/th.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jVVTwNkGABxQFM8h9aPRJ1fS2Pk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/tr.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			4d5pvCFyTpDBY/o+3RsxJ8zqLdI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/uk.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7AbLARiohKfq0KKpDOoXJxq3L38=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/vi.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QTbqZVk6kNI316gFQkdgXuOnQpw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/zh_CN.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Sf0rT0s1gLnj2sAnAlTyCH4vbP0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/zh_TW.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lC5m5fk6gdtQtz8GphGC/3aPagU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleUtilities_Privacy.bundle/Info.plist</key>
		<data>
		TBcdyXkL2WPgXif2FzrT9JGWTmU=
		</data>
		<key>GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		9Dge7JFNlx7Vk430tsjNsK3d0Ng=
		</data>
		<key>Info.plist</key>
		<data>
		WNSZEbMyrAbAVlNSAM8aNIfIfkA=
		</data>
		<key>Mixpanel.bundle/Info.plist</key>
		<data>
		n0+zesyiyMccOPW3rHWVJu54M6M=
		</data>
		<key>Mixpanel.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		CLjGR6ANQPfuQZ/E49Z3qKLsq/M=
		</data>
		<key>NordicDFUPrivacyInfo.bundle/Info.plist</key>
		<data>
		XgINhG3N5eWOnD8/+n02tMTfSAU=
		</data>
		<key>NordicDFUPrivacyInfo.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		C4qdFN7+RwBI3XPE7vZMiY7U1c4=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>PostHog.bundle/Info.plist</key>
		<data>
		SyoSvoyrq6P2VSzqGSxFU0vuq84=
		</data>
		<key>PostHog.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		XdwEI7KKJu2gxulf6d+8+2HpdjQ=
		</data>
		<key>PostHogFlutter.bundle/Info.plist</key>
		<data>
		knxKbf7GJyJ0eil3A5KoDUHLc8w=
		</data>
		<key>PostHogFlutter.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		XdwEI7KKJu2gxulf6d+8+2HpdjQ=
		</data>
		<key>SDWebImage.bundle/Info.plist</key>
		<data>
		hqH/x+9CJQSUpKnN4qNsw1pNYnU=
		</data>
		<key>SDWebImage.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PFHYbs0V3eUFDWQyYQcwEetuqEk=
		</data>
		<key>SwiftProtobuf.bundle/Info.plist</key>
		<data>
		EMl0En5gC3pM3byyhw61A+KFJBQ=
		</data>
		<key>SwiftProtobuf.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		F6mYXr6EQZyLKcQNM0vmTGcGMns=
		</data>
		<key>SwiftyGif.bundle/Info.plist</key>
		<data>
		YrdtMcGP5rAgt8xTzb9n84N+q3M=
		</data>
		<key>SwiftyGif.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		i3YpomTpJHpRGpdt+oGaesmknBg=
		</data>
		<key>ZIPFoundation_Privacy.bundle/Info.plist</key>
		<data>
		lEDTJAqH7i6i/MS1ArQOblShC9s=
		</data>
		<key>ZIPFoundation_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		iPBMOkzjkklLHQSxS8UpseL0otM=
		</data>
		<key>app_links_ios_privacy.bundle/Info.plist</key>
		<data>
		j6Z9mOuKLpat6fHD+X+2dlcOzrQ=
		</data>
		<key>app_links_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		W859JDVykxWZ0aYvnTEJCgiAYio=
		</data>
		<key>connectivity_plus_privacy.bundle/Info.plist</key>
		<data>
		JUagCk8npzjxlJea9ytCoJImKkk=
		</data>
		<key>connectivity_plus_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key><EMAIL></key>
		<data>
		bGk4Mus/4PDUiWkbFBLmlmbiCYM=
		</data>
		<key>devAppIcon76x76@2x~ipad.png</key>
		<data>
		kY6u8kk/ejBwxEfO/jd4JKCTyQQ=
		</data>
		<key>devDebug.xcconfig</key>
		<data>
		RHpP9quuxqWsyvu2j581vItYnHw=
		</data>
		<key>devLaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<data>
		CbooSLtw8fnqJ0/mR5sDoeS73UE=
		</data>
		<key>devLaunchScreen.storyboardc/Info.plist</key>
		<data>
		n2t8gsDpfE6XkhG31p7IQJRxTxU=
		</data>
		<key>devLaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<data>
		ZVgM1+KwZcZnwhgaI0F7Bt1ba2c=
		</data>
		<key>devProfile.xcconfig</key>
		<data>
		N5UWOMm+mkV+OP9Jaht6AyrPlI4=
		</data>
		<key>devRelease.xcconfig</key>
		<data>
		N5UWOMm+mkV+OP9Jaht6AyrPlI4=
		</data>
		<key>device_info_plus_privacy.bundle/Info.plist</key>
		<data>
		XH0/+6pnkfEpTKhow/XOAUjW/hY=
		</data>
		<key>device_info_plus_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>file_picker_ios_privacy.bundle/Info.plist</key>
		<data>
		m5TwcQFpHrsaji9sYUMP3GHQhp0=
		</data>
		<key>file_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YIiJ5tHvqBeSpBm2mcfVZdaGz3E=
		</data>
		<key>firebase_messaging_Privacy.bundle/Info.plist</key>
		<data>
		ys93AdRp23X3exCzvYiM8It+X1w=
		</data>
		<key>flutter_native_splash_privacy.bundle/Info.plist</key>
		<data>
		r14JfJru4nk1YXVyO5kr8gLFxLo=
		</data>
		<key>flutter_native_splash_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>geolocator_apple_privacy.bundle/Info.plist</key>
		<data>
		x5cMVsG2QGiieTBTBL+JXuWYrm8=
		</data>
		<key>geolocator_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		KFiVi4mWKmBFkTjfe3H4jsOvLNM=
		</data>
		<key>google_sign_in_ios_privacy.bundle/Info.plist</key>
		<data>
		QWxfTbul0BDTShTsQK4MbDkJjx0=
		</data>
		<key>google_sign_in_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>image_picker_ios_privacy.bundle/Info.plist</key>
		<data>
		hngPsj4a0HXcx4sU6ZCQe66TUCo=
		</data>
		<key>image_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>in_app_review_privacy.bundle/Info.plist</key>
		<data>
		mQL0MXKVSrWZQ388D3HWT45s5/k=
		</data>
		<key>in_app_review_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>map_launcher_privacy.bundle/Info.plist</key>
		<data>
		JfiDcyNrBfuIpUD4focGrc7LjNU=
		</data>
		<key>map_launcher_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		KFiVi4mWKmBFkTjfe3H4jsOvLNM=
		</data>
		<key>nanopb_Privacy.bundle/Info.plist</key>
		<data>
		vE9VNYKoOi+dkvM03doEH1VMjmI=
		</data>
		<key>nanopb_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		KY5lfwC2TvsgFj4wt7hkMmainbs=
		</data>
		<key>package_info_plus_privacy.bundle/Info.plist</key>
		<data>
		3fvXFbdllim9Ss4h/XNrgXiw0fA=
		</data>
		<key>package_info_plus_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>path_provider_foundation_privacy.bundle/Info.plist</key>
		<data>
		4u6bPkVeXI97rZCOVglyAR5OT5g=
		</data>
		<key>path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>permission_handler_apple_privacy.bundle/Info.plist</key>
		<data>
		GCjbcsRwpAGUafgv7tJLUQbRtys=
		</data>
		<key>permission_handler_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PgAJpgZlblxKbgx9eihlgflAQU8=
		</data>
		<key>prodDebug.xcconfig</key>
		<data>
		N0f0eeDiHXRz7zA5//MiQXzaaNw=
		</data>
		<key>prodLaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<data>
		zh78c3LBTHkfuaw2irH2EjjcUMQ=
		</data>
		<key>prodLaunchScreen.storyboardc/Info.plist</key>
		<data>
		n2t8gsDpfE6XkhG31p7IQJRxTxU=
		</data>
		<key>prodLaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<data>
		ZVgM1+KwZcZnwhgaI0F7Bt1ba2c=
		</data>
		<key>prodProfile.xcconfig</key>
		<data>
		pTf6eerNSPjWwOVZbBHek6dunQY=
		</data>
		<key>prodRelease.xcconfig</key>
		<data>
		pTf6eerNSPjWwOVZbBHek6dunQY=
		</data>
		<key>share_plus_privacy.bundle/Info.plist</key>
		<data>
		diUM/vhvC5DjNWFzPHWvCn3uCRk=
		</data>
		<key>share_plus_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<data>
		BP9h5Tad9Ik4bu9fKUaVSbTKywc=
		</data>
		<key>shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6uLTlq7fgHHWA8emYDf4ImHC+AY=
		</data>
		<key>sqflite_darwin_privacy.bundle/Info.plist</key>
		<data>
		33MahymQiyEybvb8abqZhtODhwI=
		</data>
		<key>sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YIiJ5tHvqBeSpBm2mcfVZdaGz3E=
		</data>
		<key>url_launcher_ios_privacy.bundle/Info.plist</key>
		<data>
		omBv5af1ABxRJRKcQ+dyMfFZrNM=
		</data>
		<key>url_launcher_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>video_player_avfoundation_privacy.bundle/Info.plist</key>
		<data>
		VuP1exYJX9kkVigYr9tv15ZmqjI=
		</data>
		<key>video_player_avfoundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
		<key>webview_flutter_wkwebview_privacy.bundle/Info.plist</key>
		<data>
		MD+Byy+jFo92G+AjSJbHazRjYMs=
		</data>
		<key>webview_flutter_wkwebview_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/LX0ZlwxwIAIhjZaDB8EiH5KpXA=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>AppAuthCore_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			knto/f5lJuB85bAOaWYjtZv2bBCq8KAvc/YXdBMhkRI=
			</data>
		</dict>
		<key>AppAuthCore_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			Wt6WL6rqHt273QbEz4qHLOxVT2TFWgDgATYATTeOHO0=
			</data>
		</dict>
		<key>AppFrameworkInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			0qJzGgfv32FLZJvtyKZ5l6s1VfiGakjo9h05xZQkLiM=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			s/honzKWbbQHFIlOzwdy3GF3KUdnP73QNagDMRB4ny0=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			sJ8jbZouR6tT09iw1E20b/5t5b8MbpqEyqU0ihgYFrA=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HyVdXMU7Ux4/KalAao30mpWOK/lEPT4gvYN09wf31cg=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			VPNjf2cf66XxnoLsT0p/tEi7PPwPsYDwiapXH8jwU+I=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			BY/hOMO0FcCl8mCMQqjVbFeb8Q97c1G9lHscfspHFNk=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			PpvapAjR62rl6Ym4E6hkTgpKmBICxTaQXeUqcpHmmqQ=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			y90o2JQjssm+7ysnziyWCNMNbGqdLnZ595pTgURE5T8=
			</data>
		</dict>
		<key>Config/Dev/GoogleService-Info-bak0809.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			t1QBC71Vpldv9tCq4ho66ccf78b+f3dkOHpUMVmh6jM=
			</data>
		</dict>
		<key>Config/Dev/GoogleService-Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			oijYBjudjSA97tyM86qihvh8+nsqAnqLrLRy79Smlio=
			</data>
		</dict>
		<key>Config/Prod/GoogleService-Info-bak0809.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			t1QBC71Vpldv9tCq4ho66ccf78b+f3dkOHpUMVmh6jM=
			</data>
		</dict>
		<key>Config/Prod/GoogleService-Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			0I1+asJmWNB040Tt20xKrAk26BWP+alzr/xtM5wlAzM=
			</data>
		</dict>
		<key>Custom.xcconfig</key>
		<dict>
			<key>hash2</key>
			<data>
			NccPiqcxX2NadSxtReCyoOLuigd1/hJCauJlV9WH92c=
			</data>
		</dict>
		<key>DKImagePickerController.bundle/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			+rujFR7Iu60zQja7xY+qXHgvRqcHXvtGUQslWQWBqis=
			</data>
		</dict>
		<key>DKImagePickerController.bundle/Base.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ELjMRLCkLklsKSO6rFYxrtU1AXPGCM/jJcf6s27mQfE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Du9cE5UhAR76HElLKo6oUOeH2BVcBFfwz3WeRTImUOY=
			</data>
		</dict>
		<key>DKImagePickerController.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			S5/UHVcXm+A0szmeblzHSHm8fdkVgUuu5mWh9E35hUE=
			</data>
		</dict>
		<key>DKImagePickerController.bundle/ar.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8EBEfhbg9Wp48uZkSBXh83/CjuaZbY0Pl5MP0h7k198=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/da.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Se5q7h7rJFaZACyuxQHvVnbMpZuFEPY2ghUK5n4o08I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/de.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			H7oPc9FDMsyBTv1ge4zzQ9mwsX0FoBuLZ9b7IesFuOM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/en.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			wRktIAnVAnqy13yInBFBAY0DrBHpEtI9oKtzLeXD3RI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/es.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			TYau/PSO1F3K80YkOSGGsuUdKr+GbcbO0xkmsoVguEE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/fr.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			XeqMTtiKSK1J6ujdx7yhXp7BWuvKJYI+RQnYiYny4D8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/hu.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			koXD1oSMQ6BAgGJmcCgH62mWYajI2uBMC9WX+USGCWU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/it.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nhSK2bw/+PLGTtW27tPjkOk71T+MncfskpqPwK2lhZI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/ja.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			KeL0orI52PODaxhrV4RvgbgC4Q6krDkO/sprxqlRPfU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/ko.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			FCrA4Gc0Mj90bRXELikqH38ISm+VZn/fTFdAWqFNWgw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/nb-NO.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			djVG/NpWtRWjSA3G1iIg53ggesmLPWxj3L7oc2E6h8M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/nl.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			wObmt9BBd0IFYimw+uGyKGjKEALrJsjZa+4YtbpoR5g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/pt_BR.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			n6iu95SLuBRFRQbd6crMWsW4I6N5HJDoNF0YNzoYpBw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/ru.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			M12gTcd21LAS83vmg70IxBhMrkr9SUy+lMNgqQqgJn0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/tr.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CuIDAgluqD/JKtE9pTTvnu9rGSot//IS3XV5HYlNKFU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/ur.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			OX1UoR/0JwgfTkhVAuVLC8rm8pkso6MssB+Tb+Kemxg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/vi.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			N5fi2LsY43ka7PJFMBqTc+GjU5z417xGXSmegrxxUJc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/zh-Hans.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			gW58yg4AYSSFypzzxa1sQ8teATRYb34fi4Jrfwasbuo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKImagePickerController.bundle/zh-Hant.lproj/DKImagePickerController.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			VoNMNcj3ugOpQ10JjxUvPX5MoSBLftKJdiq9sJ1ijsA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKPhotoGallery.bundle/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			Bfl1YdTfz0kbWyTJ8PaM6VG4RusAZsIZlzIMHkXYsxE=
			</data>
		</dict>
		<key>DKPhotoGallery.bundle/Base.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			SDI8mZH3KxLV35hSqjP1DaoT/Ur7RH3bmV+MnjMnx54=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKPhotoGallery.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			rfFrY5op8/mhQKl4lqXXPOT/E2jL9Aq2+8Xeom1sKVg=
			</data>
		</dict>
		<key>DKPhotoGallery.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			S5/UHVcXm+A0szmeblzHSHm8fdkVgUuu5mWh9E35hUE=
			</data>
		</dict>
		<key>DKPhotoGallery.bundle/en.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			79OWR8uzUiipYvXTl3V4OfE8HGNgQXp4Is5CjRpErmE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>DKPhotoGallery.bundle/zh-Hans.lproj/DKPhotoGallery.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			KI058+XFexomjnRqlnWcg5B3sueg9C1fAlugBgmGNzs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>FBLPromises_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			J7ZlNoNuZJ9b/PSG2dqFj29FgP2y2U+5GC7SxzCW+iE=
			</data>
		</dict>
		<key>FBLPromises_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			dLDNcvwjwe8wLyLuJ1P2GBfNxa8P96fy0GMrUk+4rOo=
			</data>
		</dict>
		<key>FirebaseAuth_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			f0klWRCXEQfcn63vDYAUAiIEOv8/LzfHcNFz83YzWGo=
			</data>
		</dict>
		<key>FirebaseAuth_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			BMFi6vpJss7QZjIxqJzJ9Xk1BsgCtoSGcZtdoGCK79U=
			</data>
		</dict>
		<key>FirebaseCoreExtension_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			6gboKfArqjrfFHRWsX7fjtRrDflLx9nz+dj1385QvU8=
			</data>
		</dict>
		<key>FirebaseCoreExtension_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			E3fszt/4+DIPeAQOnJ0+F+zA6laVlLVq8O8XvkQZfHo=
			</data>
		</dict>
		<key>FirebaseCoreInternal_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			j8T7QIqDjDDVZayJ8TN69RliZiYaswH2PLMkbnjceLM=
			</data>
		</dict>
		<key>FirebaseCoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			W3/peUI97ePgivwppC8A9ghiddxUioTCl3QjWGPu0+8=
			</data>
		</dict>
		<key>FirebaseCore_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			zT9eKUZpkFcOFEm1etql41ntmOvFS6b5vkAWiIjEEug=
			</data>
		</dict>
		<key>FirebaseCore_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			EeMfX2tg6A69WQFUn85QQ/mvPmg/h0AilFAAtAUwbD8=
			</data>
		</dict>
		<key>FirebaseInstallations_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			FgK8Z5YLC6IqsMZRs45PWuy+VblJMhOnddxBwGOlfZQ=
			</data>
		</dict>
		<key>FirebaseInstallations_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			z7s8T3ambVNpi66R9xEMAPIUjm5vE619MlkpCbwBDlE=
			</data>
		</dict>
		<key>FirebaseMessaging_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			J3qSeAOzUvL86fRzjQ8bFoN5GeIMwFsJ+9ZyFCvLhsk=
			</data>
		</dict>
		<key>FirebaseMessaging_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			mHYgljcqwn9eiIxzctUS8NP1zzKDxkAGBw9M3hpyoHE=
			</data>
		</dict>
		<key>Frameworks/App.framework/App</key>
		<dict>
			<key>hash2</key>
			<data>
			UzK7J3UCBk6TMt7PLiJTAPbK3D72ASCKH7O2yWxI64s=
			</data>
		</dict>
		<key>Frameworks/App.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Oc0JmtHE3YpfD+F+SYuUJ43sJ904w411ff6zUsmMUiE=
			</data>
		</dict>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			GtJkYSK8jBDQQwLeS89MYPsOt0p+jubgodnGKKfUHBk=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			2+dLco/KhCZNNQJc6K0CAizh4wsD62mM72vMgW84IUI=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UVobhZIQxHVWtWZoSlbm8Dcu+pRC72OKjYKTy69p2PU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Iy9Xy0OEIfe84FcAKnxcJ59hczMJKNvdLKLAuzWPxMM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			dhDc1O2JcKRfl83vbxLzqxIBHtk9QNJQJLoiMSceYDw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NativeAssetsManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lUijHkoEgTXB2U+Rkyi/tirix7s8q5ZVfHlB2ql3dss=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/device_assets/frame_lib.lua</key>
		<dict>
			<key>hash2</key>
			<data>
			9pJHAywFaRz7dXSnFW2wQ5e1LI8cND31Cc/ov5NZggQ=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYBLACKITALIC.OTF</key>
		<dict>
			<key>hash2</key>
			<data>
			Uez9lEXkm4ZJWmK/Xfz39ubKzWTEY4o599GJ+/cHsoQ=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYBOLD.OTF</key>
		<dict>
			<key>hash2</key>
			<data>
			qYEAH21OcCkv0m+o4FQ3xKnw+MWYECf/DO6tAbswMR8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYHEAVYITALIC.OTF</key>
		<dict>
			<key>hash2</key>
			<data>
			9xQ7zVNSKDiWuwQftHj+R9z7HwvXzM1bvfkyo8GGlZY=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYLIGHTITALIC.OTF</key>
		<dict>
			<key>hash2</key>
			<data>
			LxQltGSLdyrwfLmW7JrICuweYdtaOI8tDynqIaqkl1U=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYMEDIUM.OTF</key>
		<dict>
			<key>hash2</key>
			<data>
			y/0XkZhW787avdc3gdrSmEMmkornyWNb72InsafXHSE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYREGULAR.OTF</key>
		<dict>
			<key>hash2</key>
			<data>
			caIWLIUuh7qmRAyYPkBigzkWRneMi8dOV7AGE6NBL4A=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYSEMIBOLDITALIC.OTF</key>
		<dict>
			<key>hash2</key>
			<data>
			QYGqwPCkdY4cj+8Mru3rwXCTGWRAQxMVVtaO9gYhxLc=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYTHINITALIC.OTF</key>
		<dict>
			<key>hash2</key>
			<data>
			dRNKKtLG4BayJQeNAP2cLq7Xv1r5UHQspnNaAfcBUwI=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/1.mov</key>
		<dict>
			<key>hash2</key>
			<data>
			obrUZzSFxl96MU275J5te2NeC8gmTxH2Ybc9B+pcUv4=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/2.mov</key>
		<dict>
			<key>hash2</key>
			<data>
			D5YbTtIxc9ydaQpRHnx8QMQpumaggXjuZLPW17wHt1I=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/3.mov</key>
		<dict>
			<key>hash2</key>
			<data>
			XA8OyLaBm0ZNZnzQzEHyHAQ3osX0XZgt3JIxVMV2dQc=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/4.mov</key>
		<dict>
			<key>hash2</key>
			<data>
			sNBJsUQODKOIRi9ZATJzejEA8918jSlgScFC8uWlzAI=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/5.mov</key>
		<dict>
			<key>hash2</key>
			<data>
			Wce647yFmi88Fzr4OCl6Y/Eh6N8OfTrhOjXbz6nxuwY=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/Logo%20Text%20White.png</key>
		<dict>
			<key>hash2</key>
			<data>
			WPgVkhEL3MdDeyA3se5zkciDXsNz6doqi3KWpKuja6Q=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/ai_magic.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			gSVITSiWLDPGRlaECnRLr9f3XwuzrWko1dKQXHAR6m4=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/app_launcher_icon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Rq/HRdjx4AkoFXf3utG35frVHGXGcd+pR693ivs/K9M=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/app_launcher_icon_old.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ezqb4pzz5ifslgZ7CkldZeDS7bc670cjKHe5swT32JA=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/app_launcher_icon_v1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			VYgmc+Uo13Qg4fdlxFGwa+lkU0NdwVkAFt0a2lbDK8o=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/app_launcher_icon_v2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			HrdSaWlQNOrPeFPmGOlx9hv9uLRnmi3ciGS0/2MyR2Y=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/apple-reminders-logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			/Y8LKXhsbxo3E5eQjcnx1j45ZB5hZIHqCkey42o5bi0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/apple_logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			WO5uYO+UNGJkN/GqrYEICarXcudPt0Sy6ENzcB4xT0A=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/background.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7KXyRT5MTYxnuigZ0Fzyar4TMF0wUxqV9cGDFzh0+ms=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/blob.png</key>
		<dict>
			<key>hash2</key>
			<data>
			cvevRFvnCASUC//GPfIUff8TF6iByk3WE+RzaBuYsn4=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/calendar_logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			WWRMHLc9GLLUFxFNVIecLCyxER1+g7PcDNFoKFyI2Ao=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/checkbox.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			jW6eSHQrRrhsBNEDsj3nLvWZT2B6EfXc2R4DkvU+Eeo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/clone.png</key>
		<dict>
			<key>hash2</key>
			<data>
			5YyYNYdQ04QI1SoCDMxS8SVuuffNR6lUqoI7edD5J40=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/email_logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			IH8yqN7UM78emUEEkzL4iVjafDHyYRVypKWjH9MwrHo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/emotional_feedback_1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			NkdCoMlZfOSlNNMxFh2ZD34YJadvPBCeylgoyZlWSd4=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/facebook_logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			T34ygalNHbTTlwQPzyCiHmrgwxGa5Im1HE3nlJhVd78=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/google_logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			zDCnPR/TZT+VTDoQtr3fIvyJWO+K6wJNJW9+EmNCNyQ=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/gradient_card.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ie1oAvYnn8NLn0RoLgsY4FWATUIfzU21CIpWl5+5uXo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/herologo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			j0Ft0W59ViVLxq/oIivd0xhHalJxl/GB8qxB9POvx3M=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/herologo_v1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			UL2tinCr7/SZQIYAgtxhupNHt5ohZrrM2CMHZVM9WuM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/herologo_v3.png</key>
		<dict>
			<key>hash2</key>
			<data>
			oZcrsjHUc0PuH+CGbOAWf92v+KxSL2Asf+Kpb+/wHnA=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/herologo_v4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			il15Jf020HGWDiQSCvx3H1CovuAt61AKzXrTD6HpwZE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/ic_chart.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			WQzWmOIy0xO0icyi3QflV9y9I2OdIHGQubsFTh4rY4I=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/ic_clone_chat.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			rTIRe4qAzw715f3B5Qh/mG+ryDpGDRwUYvKLwqH7BYU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/ic_clone_plus.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			AduLR5DhyDrdup+VPSDSyMRGR5tgTrfjLLWYf2nE84A=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/ic_dollar.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			bEeUnMLB1ivj62uA4mCpSvk50DuhVIcuDfCY8L7gNYs=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/ic_persona_profile.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			5wT2Cy/fISPZRh6/ajjpfGVntCFjhJLZJ1y8CuhMH/g=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/ic_setting_persona.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			yTESRbsS9Gih1PL6kUz7WcR+p9P6koyXdgEdRVp85fU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/imessage_logo.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			/yWby5gGrJZbQYXXdWf82zTrDHd43AeUI3vcLO4xLIQ=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/instagram_logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			MsACMLkTbWjScMTrQGcyw3r9/AmLI1dEcrOd8T9aPl0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/instruction_1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			KIrYzf79rt4Ruw3xZSHnxYQMRlVSDS1kTwNnNuKn00E=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/instruction_2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			8KI98wUKoxNDim98aaxRyo/WBcqbG4Qv3IZY4SomY9Q=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/instruction_3.png</key>
		<dict>
			<key>hash2</key>
			<data>
			G9cmlro3iKkGZdgrg+5TQbTDCET3GX34Nmr49NuQ1bs=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/link_icon.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			ybAGbbcSOjq37Uoe5d8COhWCStQytfxOonK6KQJwLOg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/linkedin_logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			0ZTrCR70B2rMKBX0mXL06jgdaNRHtdCGQWkIzn3ozbs=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/logo_transparent.png</key>
		<dict>
			<key>hash2</key>
			<data>
			8+VUV834tggy0NB3bMHKDHH6x1qBYTEqLM2Dzzy8k1U=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/logo_transparent_v1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			cyQJQy15xrmaDWISZNQzahpGWWW8gY1nTIROAEwke3g=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/logo_transparent_v2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			I1ltUQj790pSfa9Hnd5EV1ozhIn+hlk/jyeerN0GEEo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/new_background.png</key>
		<dict>
			<key>hash2</key>
			<data>
			5aflXnO0Y2GuChrywUyAPxsbfS0yGdFHA8AqeoeHjno=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/notion_logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			yhjTIxpFJ4QHyr1M7RMUNpsNRLGcNtQnC6EI0GRJeNc=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/omi-devkit-without-rope.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ip0qtFNhsZdHj7UTn2JamRTN0duBMhQN1+Nge6FQmCc=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/omi-glass.png</key>
		<dict>
			<key>hash2</key>
			<data>
			a8P4O80AnC5wXmVBM9xWTNkJ9GEWLQVX+VVVbDnlnik=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/omi-without-rope-turned-off.png</key>
		<dict>
			<key>hash2</key>
			<data>
			2C5cR5GONyq9MgNBZCmzedyMManeStugcKOYEmJxxbw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/omi-without-rope.png</key>
		<dict>
			<key>hash2</key>
			<data>
			6D5YGkOIWdInMv1EckUqgiAEOkwQ6jziptm+AJSVEM8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-bg-1.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			gYCK0bfl7tYwE3/YBqSEWSgM8p5NPSOTElpg8lyGQcc=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-bg-2.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			hhhGGlMew6Hr51vznAr8bXBdh01jzeRWd4mPHOzu3Xc=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-bg-3.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			f1Jrv0dluEgJoEQ8dcMyCdch0TyY65Cn6sYHDRhDm8k=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-bg-4.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			+AxhuMCfoshrgDEgpJpBcntiwrx13dUK86HQj1j3JU0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-bg-5-1.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			l24+7nr4vRfMhqAhx05jHxJxqwpyfM5V55Vvhb1/LyI=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-bg-5-2.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			odCFZifxNISs7eY3xu2voGheiYgl0gHRabCFl3GJNR4=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-bg-6.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			4dQ8vfwa9QEX9jIqsdSbv/3z633Z+wMWCrFBcJftv/w=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-language-grey.png</key>
		<dict>
			<key>hash2</key>
			<data>
			i/YD/eoEZ2y/+n1X7MrHvv1HtnOfWFiaW1Vl8zrmvRY=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-name-grey.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Sa0n6eqEDbAkJSj8tiNa0ViwKBRGI59OOtuhn/bzzEs=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-name-white.png</key>
		<dict>
			<key>hash2</key>
			<data>
			htfqLdP1PmQVr2qVhTDzG4n4pXc7R/MHE2XnEKWooPE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-name.png</key>
		<dict>
			<key>hash2</key>
			<data>
			FFo+RvLqCu8rDLMugxVSX66+LgwubTvnGsUNQWRrwgo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding-permissions.png</key>
		<dict>
			<key>hash2</key>
			<data>
			0Ko8z85EIpPA1Di+NGbtjyPcg1DAA7ff0qODR6tBCJI=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/onboarding.mp4</key>
		<dict>
			<key>hash2</key>
			<data>
			xwLXS6WCwlN0fuC7p52veVjcdI36yWC2O9+oO0EeuWw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/recording_green_circle_icon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			9JiviQmIKy2FRvMc+Rt5l8fE8GpA0jkDGJEoqmoC9JA=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/slack_logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			27hbOnwpOqwvzH6IojOXKuEewV4xhwHY2CdCKkQkoFU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/speaker_0_icon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			U0svt8V5vKMyCNEIXrhlwf3rjTEgXXVypvGkrropFAU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/speaker_1_icon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			BO7uOYhfTb+kC34byDhBrEolp0E/owFIGbEJuvQ6Ud8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/splash.png</key>
		<dict>
			<key>hash2</key>
			<data>
			OConwWZSIetEjlUx7vGzIRr1BXdJvQt2FVS+Rr3Uz2w=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/splash_icon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			8+VUV834tggy0NB3bMHKDHH6x1qBYTEqLM2Dzzy8k1U=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/splash_icon_v1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			pNHpZCzyGtTEZlBLe8Iz8fpWHyY/kmGIY9eFFZOfugQ=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/splash_icon_v2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			I1ltUQj790pSfa9Hnd5EV1ozhIn+hlk/jyeerN0GEEo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/splash_v1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			TJxKA6iJRjpMr5bTzaSEIv8hgFFuLYI6q8c8yJOFoP0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/splash_v2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			NgSCZ98qUKjbRMmgZXzGHdypHa0WiD2jCbSBsSaQ87Q=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/stars.png</key>
		<dict>
			<key>hash2</key>
			<data>
			aShhgkkocOAmP2ObrgPtYXgAcrn1MD75IEjb8Kp8h9Y=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/stripe_logo.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			nNacp4luj4KAHnEkUpwXpOqIcz66GhnSUL7Dqt0vs+k=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/telegram_logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Z58dg6dafRBMJiuKRAtXiOpYJqcGXhBSZqcdBJK0l8o=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/whatsapp_logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			d/zJ269N2Un2VPnTkfaZwei+/378FJqiZXH939iOAQg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/x_logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			QyvdRyVbSDZoQ+wh0gnndPk7gH4f0IlPyYOq0Nx70Ds=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/x_logo_mini.png</key>
		<dict>
			<key>hash2</key>
			<data>
			br4jwYn0xOqD1o90lGyoKJlhl6oB9Hn3GRg8WafOvlw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/images/youtube_logo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			N1zYPl+36ls6J4uuj/G0LHSLU9qapeYzjO97tVCH4DQ=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/lottie_animations/no_internet.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IbHjIjKfoqzmkVkdSvuAWqt0sjPVzkkFn0OkdCxYgwg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/lottie_animations/server_error.json</key>
		<dict>
			<key>hash2</key>
			<data>
			e45HFMPTGyqHFVCvJzV9nK0tIvMzBI4D4/SwYn48HUg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/lottie_animations/wave.json</key>
		<dict>
			<key>hash2</key>
			<data>
			9BRvVVpdattM+bd7kffAD9x6ERyGFChpGvtUYNDY6+0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/pdfs/favicon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			erJSX0uGtl0+THA1ihfloar29Df5nLzARtrXPVm7kBU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/silero_vad.onnx</key>
		<dict>
			<key>hash2</key>
			<data>
			o16/Uv085fFGmyo2FY26dhvEe5c+ozgrMYbKFbH1ryg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/silero_vad.v5.onnx</key>
		<dict>
			<key>hash2</key>
			<data>
			a5nL/Tkka2cG+Y7BPHxQxrKZGB8kdPoFy8gEaswnQ5Y=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			2YZbZxoJ1oPROoYwidiCXg9ho3aWzl19RIvIAjqmJFM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			x3o75lN72Az4wV2tFy/JH3xEUQUcGNdFggXAnzUW2bA=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			J2gUgEQeeuDzT3GOAwyEnRAuvcZjPy0/iC2PBV/TcFg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/awesome_notifications/test/assets/images/test_image.png</key>
		<dict>
			<key>hash2</key>
			<data>
			rCXGZfbCude3pdbWXa+PEfPZVI3pWvaa0eT3NwQ/T+E=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_sound/assets/js/async_processor.js</key>
		<dict>
			<key>hash2</key>
			<data>
			nFPzPDf0YueFenaFmxdonuThzIl8V0qGlRuMHXgq09I=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_sound/assets/js/tau_web.js</key>
		<dict>
			<key>hash2</key>
			<data>
			XyBYDhHBsAj8c3UZXtskCRXmN1dTAlG2EyxwnemA7ZE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_sound_web/howler/howler.js</key>
		<dict>
			<key>hash2</key>
			<data>
			YZjmVroMJ1Bsv55bwHNg2r3uXxEFouqs1r/69w8Y1w0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_sound_web/src/flutter_sound.js</key>
		<dict>
			<key>hash2</key>
			<data>
			3/I+S1peUYHzFzxrUeKW9teq4JRp3ZwrTAn80oRfr0E=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_sound_web/src/flutter_sound_player.js</key>
		<dict>
			<key>hash2</key>
			<data>
			roMZ+PvWjNb0sxvVb6TKPYgUQJ1KTsspT1ITqZzPS7I=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_sound_web/src/flutter_sound_recorder.js</key>
		<dict>
			<key>hash2</key>
			<data>
			wYkahf9rk/A/ayLrLkn9o1BJXo4gUY0pJAVctp3KnSY=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/flutter_sound_web/src/flutter_sound_stream_processor.js</key>
		<dict>
			<key>hash2</key>
			<data>
			BmL2hPoed+JpGqhLDw+iBMpoMxcLJyfXxdKu7N2y4t8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			NCokbUhf77D/zQwBLqdXx/daGmIInl+zWSIBfUUJYRM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			kHMjrzXlByDvcrCd78+Giu2GvPWoYe5lhk9GryQe6wY=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			DyeV1cl3UtjVlESO1xJZY3qp8Yx44o8b+19kBxC2GN8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/frame_sdk/assets/frameLib.lua</key>
		<dict>
			<key>hash2</key>
			<data>
			rNw+ZNOZA1Gx+4PGoobwMTCkTXclbYtfVVPE7ZLXWhI=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/google_sign_in_all_platforms_desktop/assets/post_auth_page.html</key>
		<dict>
			<key>hash2</key>
			<data>
			/8CldyLooo8paLHIVvkcAgwT1wLDFFNIJXNd+H7e/o4=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/amap.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Wr031u30Pu97HBIjBgwfnFdZUpL8f4HECJ1XcOX1KoE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/apple.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			kn9AOJ7gXxabPyRlPNz8AI6mPOTv5u2OfFsR/REEHQg=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/baidu.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			00PWhidSUnTf5cU5Ejq0tBE7cK2E0UwxhRdPOT9X8dc=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/citymapper.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			v3IXMJdCLiOoByUqPyIkQPOsi9NIbDf12d6CxFWyEWM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/copilot.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			rYxjYUTddxS6l2JD9OPvjDs7wZTXPCvwIf7YKnVCsBo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/doubleGis.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			ok9OtP4jZjwf5RyrWMnQB71DQNX2VPS7oJuYqdZs7XE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/flitsmeister.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			rUg7ai30UX0/tYWwfLPyGxtuzRXBQlsPLVUGplRG9kI=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/google.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			4YF2UcZaQAMrrfSq1wQpp5DkqnSwAsywYd9J2vz0LzI=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/googleGo.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			4YF2UcZaQAMrrfSq1wQpp5DkqnSwAsywYd9J2vz0LzI=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/here.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			rUQiM33DRtHn1pBarL47stkf4YoXlTt1PrTKY8ouvo4=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/kakao.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Vbr3zaelyK5TcBLznXXvWmXWHpmmLio4vYeFf64L0pU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/mappls.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			eZUL6Zdp9GaY5sjzqfbBlPacl3BqymdBk53r3s+mogU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/mapswithme.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			uwnswDmDaltG9lGAPa0YX83TGVhoB/6hsZGceGpdvEY=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/mapyCz.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			ff2E27o3+oXJhZgW80bsW8acN0jbUbkVKaGTdsZwc4M=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/naver.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			0mdxFHYE+tzzpl+zpUEw464/kH25/wcjXbM3w+dnH6k=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/osmand.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			IgywVvb4c7fdwz2p0vYWT761a4E6PSCDaGoHCvAH4/s=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/osmandplus.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			KNUbodHA0t6qoum/1oF6w5i3bJJpwTVZEBVuVvFt11E=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/petal.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			lpUkkuYcZ/B9EDw0zZV+LsQTZtNw19h/AZLeBPpELc0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/sygicTruck.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			RdYa0PeLuPIXMgk0nikTSJj5KYC7wqidHz1CoWkXMW0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/tencent.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			vRjds012+eza82uLUkmZmZjPg/yAQ0Z0PgAE43ScAKk=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/tmap.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			lKZUTSaW5WqS84CNCZC/UhkX7VO8AsT8yCWWb4Lw8pY=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/tomtomgo.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			ZO9H/uHbY7CL3t8h5tPHvRkSwdY42PiRUImjlJKuoWE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/tomtomgofleet.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			xBGGeqiur0fxjC6877GAo7MH88eD69084vQyqZ5rTpI=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/truckmeister.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			GAfXSEQJrRwoGhFipC7VOU69TW4MON/adHBLED36qZw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/waze.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Z8YMJsOo0T3s5jSRlvoekkbT7Zt1ISsdKs3cOPtpkio=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/yandexMaps.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			HJC731UCd/iwO+ADp7Cjj642pOfMU7NZf000qMzVVEc=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/map_launcher/assets/icons/yandexNavi.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			YZhgQ+uvAED4rFRgFndcPtLk8JBV4rYMm17ikzH1BNE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/mcumgr_flutter/assets/mock_logs.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			OswJUB5/nuWFuSXfitZEtrHNQjJ8E0fLUwacFXnIqiA=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/mixpanel_flutter/assets/mixpanel.js</key>
		<dict>
			<key>hash2</key>
			<data>
			PCz+XanT9gCOPni/vgzGgj+1aMErmrn36cskihIV0m0=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/opus_flutter_web/assets/libopus.js</key>
		<dict>
			<key>hash2</key>
			<data>
			LOEk9yxZ1iyoAedwoom9Z/SoV6klxg25Ov7DEkMOLI8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/opus_flutter_web/assets/libopus.wasm</key>
		<dict>
			<key>hash2</key>
			<data>
			ARQyeNo0+qYYiXzhWHUFcnESKVkTVVTeBlyo5OBN/Nc=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/opus_flutter_windows/assets/libopus_x64.dll.blob</key>
		<dict>
			<key>hash2</key>
			<data>
			eULAEQpYNcfyoCc4LxW4UCRLyAcS3LaIt5zWHHMmNxI=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/opus_flutter_windows/assets/libopus_x86.dll.blob</key>
		<dict>
			<key>hash2</key>
			<data>
			wv7wdLxal4D0lfF5hEY7Vu/dOwrclsta1qV1QBd0QEI=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/opus_flutter_windows/assets/opus_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			65NmnnKsgCPa5u4YZNcBjHddOruW3/IxTK0+A8gnwvQ=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/win_ble/assets/BLEServer.exe</key>
		<dict>
			<key>hash2</key>
			<data>
			ReukBEu3D5ajKfsy2xp5kylc1wKzOSn2Z5BmlbkaQn4=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/window_manager/images/ic_chrome_close.png</key>
		<dict>
			<key>hash2</key>
			<data>
			cP4BMLu9ko0EzTOkns3kIuxU/XSLek6YP04xvm5z9fU=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/window_manager/images/ic_chrome_maximize.png</key>
		<dict>
			<key>hash2</key>
			<data>
			k/LtAS7AEoi3itSBbvJUJh6f8l6KmFg1m0VDHJpd5fQ=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/window_manager/images/ic_chrome_minimize.png</key>
		<dict>
			<key>hash2</key>
			<data>
			CXbtu5l3E2VErxfeEl80WkEGVpTekgNtk2WBfqbY8Fo=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/window_manager/images/ic_chrome_unmaximize.png</key>
		<dict>
			<key>hash2</key>
			<data>
			PTdZMMUU7C68BgOtHhOYtNr0WJUQQqlyMtFvF+HJYDs=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			TGVjYgE+Oyl6guvhhPPrWfynkxkJeFjSzSLsQqn7Q3M=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/shorebird.yaml</key>
		<dict>
			<key>hash2</key>
			<data>
			m1c/hyvGlxXZCpeyDDvuUbgiYrwAyJ4hpDOCsGRZeCA=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			RTqty/gYjcrXxgR3QLjNukvL8gGpfV6MdaYktQJaIa8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<dict>
			<key>hash2</key>
			<data>
			eyO2ttaLceyqslaxflfvk4CFsxzu3Kyb0C3QoG2r3GI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<dict>
			<key>hash2</key>
			<data>
			auaf7wPxiASCYD2ACy1dfbMJvmONwFvSz1BWYAQrrSw=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			o0iigVsmgwmtZfSv3X7hReDNYP5rXblslDnqq2s6UQc=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<dict>
			<key>hash2</key>
			<data>
			EXDk4t+7qCpyQkar+q9WHqY9bcK8eyohCwGVtBJhMy8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<dict>
			<key>hash2</key>
			<data>
			0h9+vK5K+r8moTsiGBfs6+TM9Qog089afHAy3gbcwDU=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<dict>
			<key>hash2</key>
			<data>
			kg195C3vZLiOn8KeFQUy7DoVuA9VZDpqoBLVn64uGaI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZyqlHYuZbpFevVeny9Wdl0rVFgS7szIyssSiCyaaeFM=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<dict>
			<key>hash2</key>
			<data>
			U8q/0Ibt9q4O2HMsCdUwITtJdTx8Ljhlx+0aY83fH6s=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<dict>
			<key>hash2</key>
			<data>
			RAOC6nDhZdghbAzsIZgVeq6qPt+MUNTfm/vkUnhmZO4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SqzvIxqBXEJ3U9LJ32hCEXsrH2P16gumQ+gQx6Pdlf4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nmZjZpvFCXrygf4U9aPkNi8VcI7cL5AtA+CY5uUWIL0=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Q4SLFSghL/5EFJPyLg7PNi9J/xpkVVfzro0VQiQHtrY=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ebBVHSZcUnAbN4hRcYq3ttt6++z1Ybc8KVSYhVToD5k=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4hl+kRU4PNNKdAHvYrliObXzSjRzow9Z18oOMRZIa0o=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HqbvCHqKWTzs5GjLAwupqEIYVi9yf5CrMdMe31EOwUA=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			+PMn+5SDj2Vd6RU8CQIt/JYl3T+8Dhp7HImqAzocoNk=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JcpN4a9sv6xynlD3Ri611N5y+HoupUWp2hyrIXB/I8Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yEgZTlCNrK/A/QBjEwNGB6ffC+A9gorPvnNgSbYuQ7Y=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			33QenaRXiMxDiT3mFuKUmhdbAqn3oTP5nW+nau6zLm0=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			0VjriRpZ7AZZaP/0mMAPMJPhi6LoMB4MhXzL5j24tGs=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			n5XX54YqS1a2btkmvW1iLSplRagn0ZhHJ4tDjVcdQhI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			UDp/mDmM6JjNWnn3CEFcUUS1OVuawmtENECFGnWaehk=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			wSU3Ai74GJkae/7UGnbY1q6WL/vA5lEax2Kl0IRef3w=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			CPeumO1XaDkrx6/fJcE+OizzcXGN7WGEGRKA1eNwnrQ=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/Instabug</key>
		<dict>
			<key>hash2</key>
			<data>
			73qdvkscigmBijHtOIyPFrUg7et43tdnqisEnKHr6F8=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			T/T45EUEgmeXnFCSblpf+IAYcrvPKIrHF7JcJPm2rxo=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/Config.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			kmHsztpgjvF0JW5f3HdMHm49z1M0CcG8OT1JDQHHE/E=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/FID.js</key>
		<dict>
			<key>hash2</key>
			<data>
			7CN8uNWQfSxqec5gNXAJcCBJlxpSBFzdOOr09UKUr4A=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGActionSheetCell.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			Iig4a4WB2008joZTlxE4U8C2/D6UYpTL1IHfCFEhl6Q=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGActionSheetVC-iPhone.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			thnwjsppFU3uFNWb87aG5QV9vxfYCJCnvSTMqqH3mY8=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGBugVC-iPhone.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			0InfsO+5kxbkIxuwHIVcdJoBOxs4kbYlhGMK9ylZNfk=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGChatCell.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			21yi/S8NG+ny7prdqeNM2VJPkjr1NhE3/5o2v7ZAxOg=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGChatListVC-iPhone.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			yaVIgHjxEwp8Uy2xpbDBjjigcZnK8iRr+VaYrtJgc5c=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGChatVC-iPhone.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			/2B9tUtc6S5JVMCUUsPIkgRZDg9B1aobD92AEa6KU4g=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGFullScreenImageViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			QKpva1AeF/u6qmCYJIbn0Y6Go8ogufaUhEarNf0lXuE=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGPoweredByView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			qjunAvW3geVx2RX/1xTre1VOycH8p/BxMtI3Ph9qYfM=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGPromptCell.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			4ZJxe77m+BW5WXuPJ9f2NpOIr3SRLHZ7ETT7vDD0rlI=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGReplyView-iPhone.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			KjkXxkFVhFvKH06+9yL7I4lDDikndg6CuBYaTj6YcDM=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGReportCategoryCell.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			x5zIX14wMBBOXTkaTR7yEo7Y6NF4bacNST/lxv0HVhA=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGScreenshotVC-iPhone.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			2QQZVP+Su6+Sp5oH0dx0RdVCOikX33H9HS/n8b833CU=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/IBGVoiceNoteRecordingViewController-iPhone.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			uU6wlsxRj2H9e8fKPmPvi+5nu7l34qG+KuXOk22HwwA=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			t2r/G4voIhv+V56fOdX2hA9QRCMOLSFZTGjNXC7ZkkI=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugDaignosticsDataModel.momd/InstabugDaignosticsDataModel.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			P3jGB4b8T5QNWHILpxgyXBLiF0GBxZmDYE4eKyqlTqw=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugDaignosticsDataModel.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			qic+OO5WHhsPRqqyKJ9hLObsahmhX39YKLFkoABZ6eo=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugDataModel.momd/InstabugDataModel.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			NuesdTYd3gmemqURPnhWsDxIoERhqPR5//ZRdCOS9ck=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugDataModel.momd/InstabugDataModel.omo</key>
		<dict>
			<key>hash2</key>
			<data>
			G4cpo3+HsXx3tEwTM15mYQHXjPCw3vPzWf6Mcc8Yb9s=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugDataModel.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			mvNWWyKAafIKbofs4NB4wTNjE7z93MWzeA9zj0JZAe4=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugSurveysDataModel.momd/InstabugSurveysDataModel.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			ZZwLVUd0OYoN1BojiKO/ep0OnKnqcRy9ULwoHCJNoj4=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugSurveysDataModel.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Y8N2vW5ZuhzAXAho3Y/zBZ0PI5g5i80inwV9dJYx8Hk=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugUserStepsDataModel.momd/UserStepsDataModel.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			J2urkE8keJVImAwVKZWm2jUMqmcqmghLWqANsm/nwAs=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/InstabugUserStepsDataModel.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			lOis0MDxpoguZJnMXEhmugJ5A5GLG/gYzZ1jjL2e6dg=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/Instabug_dsym_upload.sh</key>
		<dict>
			<key>hash2</key>
			<data>
			yQRp47oMhb41/QfLIxifnPWTqKi+hpJq0v7fyqri7JE=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			68XwNvROhusgvn9QwENIhXOLAT0MiwXvdYbHK+//gkE=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/UserAttributesDataModel.momd/UserAttributesDataModel.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			cRqSKwCv9gVnEJeQyAh/5b7rI4Q4nCkme83G+NsfC4I=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/UserAttributesDataModel.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			5lcpESUTF7dVvryJcaa9Lmcwj5cBh7a+mQqz2WmGGuY=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			vlv+4dBbBI63aQyrnAigl/xp/YGigFTRWHBrKCEJ4f4=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			J4De42pUm+TFNnzglne8N/paDN/2VJ37I2jqONCHgv8=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			BjraKk650wZ4JEDNpDVqicFBJ0wVTuMtEppHZKEGvb4=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/ar.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			FjwIp4OocFcaztyeKyy0SO9mgZHcpMTIfgW1YX34Qg0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/az.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			feygIQzDUHXndRrZz+Y0K/OAsZ9hgoO6H6mnWKP1CrU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/ca-ES.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			7Jkzs7c/fAOMrYmnmjVzZqB1RwQ6JsNnqZ8JqM2m5/g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/ca.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			klkvHLk/gxChDT4B8BXsPPUlIw1oE3mxR3rMx03H7CI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/cs.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			9ysy+FPxGFKWsk9ddoQO1+DEayo8jsYON/5VSfjtb5w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/da.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			6VGaLIIRBWOmNszgLK6sHwOe9m415DshnFEotjiu3j0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			WqIBZWBDqzaH0CgEnv1F8mXqO/JTRhtGFSCWS5xdSaA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			yLFmnR9dU5TL2FWW7D/JpGVKZMQVQtc6A3riI7hmxN8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/en.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash2</key>
			<data>
			Lr6rDy8EAt8S8WL0sGd9Nn5eViv3p1dMB5ttvTej0fk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/es.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			bpE2MmYPfIMnFTTYY4w+oWphU0KjmfrEbc+FYDQRwus=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/fi.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			l19PYwhJCFKQjPVx+nFyA/mz3viDFl/7B3Ccok7CMKc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ZPSPYHFrtC/ahYHGxCtgzEx8hrvQt3US9nTdkAGWO98=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/hu.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CCcezMSNsz/M/we2kN6SPgavOc9V3lKMtJ2F5CCDQtM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			AqJcoMQ/POWXK/I/clIUK1LccsSPO3Ypd1s9rUpbSJo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			X16eWDRm/QMzTLZCmbttUTkjOMcn+UcejKuLsrqgVHY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			zzlGuiBkIxJDRMuXTnvbXI5BFY8SX+RthaovJhJpTf8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/nb.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nOL2y2CXIu1ZnAbti0UZQS7DHvjlfR6F/6uVIK5IhKY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			vfF8XWGD93jA35fNWreTg625Ur5XySbicPCFyb2WDYw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/pl.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			mH32Hs7STnDSe6V8x9g4pY1ZM9Y5jj1W/d2c1kIfG+k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/pt-BR.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Cp+KO9ItQre7lQmcUmRut+LWrbJ3rYT6YraYni/sdjI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/pt-PT.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			T/yRtYvzKuNq0jyM4VXo3LjxM6smq1XSsSoHA2D/ycg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/ro.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			rXC43x0KNU+p3u+3QA53DPyB9EyiXkmsX/7mFdcw2Bg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			k7pLL32V9InfZyGKxk83qaZeXG3423NQuUGs4Ans1l4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/sk.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1TuDZrwKOqiXBLkr4WE45ld1emhWHH0/QgToxC6jOR0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/strip-frameworks.sh</key>
		<dict>
			<key>hash2</key>
			<data>
			Lpm4UvZn1WmEqTctsiPIEzhoUTl6oMjiLvMOsfwL2As=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/sv.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			OtMacaSHVWx0XXh3S84qB+Jv/oBpFs4amhr071rTPZ8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/tr.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			/8K5JeZ8loJX7KDhES/nwsn08aDsorf2EQ1HfwkLQwE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HpKOrLNgAXQIDvVTx0qzg2dDv8MPMZekUMw22Bq2mns=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/zh-Hant-TW.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HRUTak40GHI5pUm5GURzClJELyn/n4OcrqXwOPtrU2Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/InstabugResources.bundle/zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			CGKAgcEbel54Q9w6rVNKPq5Su4EZ1Bh4YSZ53lB/uvE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Frameworks/Instabug.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			bVOEuNKA/1oBNYdXgRP9bqflPRbJh1R8JawdJ8Bhevg=
			</data>
		</dict>
		<key>Frameworks/Instabug.framework/strip-frameworks.sh</key>
		<dict>
			<key>hash2</key>
			<data>
			Lpm4UvZn1WmEqTctsiPIEzhoUTl6oMjiLvMOsfwL2As=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			0JLrMpCB2ZhtF5w5Y3+IKMjrs30+ykcBLoT13ariN1s=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			YWDiXOXlKlzZAoICaQrdZBPwfjXgiWMuYyYsLo+b9Fg=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/InterBlocksAssets.bundle/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			iJQa6N3itUZaPfMr9X/lwgkoNTWAVClxM4WhavJxUbg=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/InterBlocksAssets.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			a4MuI9Jj150yKTz3O3mexO9e1mTtbpftkNPD7xczX4w=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/Intercom</key>
		<dict>
			<key>hash2</key>
			<data>
			gCwRZxHyEUmZ9bO1HxhCz8iheG4meBVen1qLlEdn/UA=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/Intercom.bundle/data/intercom_area_codes.json</key>
		<dict>
			<key>hash2</key>
			<data>
			HHWx2K1Imauk/q7pULg6GxUazkz5a0O+1NgNYM8xPoM=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/Intercom.bundle/sound/admin_reply_delivered.caf</key>
		<dict>
			<key>hash2</key>
			<data>
			DltxhEVySw02dT4pjXZEXjvqbmEeVQgHV9U9Vnf+bZc=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/Intercom.bundle/sound/message_failed.caf</key>
		<dict>
			<key>hash2</key>
			<data>
			z+oFmztdp/PrZbaiOCMc8hI+0ACQakOBtpeU4o92iRQ=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/Intercom.bundle/sound/message_operator.caf</key>
		<dict>
			<key>hash2</key>
			<data>
			y9EdmeincpYjEtRM3zWRwXdbQPTRmaYUR7Vp/3kcgGw=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/Intercom.bundle/sound/message_sending.caf</key>
		<dict>
			<key>hash2</key>
			<data>
			Y+4lRhNwia55OZLajS6XLXN6CBlCN2nnCqSE+ODizzY=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			f9rzNwCMv3yUdo3UQLYTPt1Jdj4gx9Lq+NOm7RNNc1g=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/ar.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			OowIUPNv2G0vTOY4v6WNe9xfA0vIf03MOurYcJtVMzg=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/bg.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			wPXnrNRC6vd9LEClBBGWeU9hsszdDSMhh5RvjGvT+gY=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/bn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Yc4Vzk7/Npz71+yXxWncK1zAPj3pUWfHCtfrDPom5Mc=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/bs.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			rV+Dt2j8Jwb2CyIn+S5hM8aitHetWius/yEAzq2wNyc=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/ca.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			YcuGY5kkCuvcaBbANggN+eY0mf1RFibD8KqKeh0c+Ds=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/cs.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			4prI7FMz72HLWa9o3V5Z5ip1mM6YEfceHWY4XBrapYU=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/da.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ZEF8hkkDraYOYGMSYv0TbjeXFTnUQar8tJNyT5USSmk=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/de-form.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			cqRBazfAHHM/17TSf6G+KIilq+1LoVgJEzVBT+0TWHQ=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/de.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			zHNknl8m0NnhbLFvY2ow4LL8DjGqAJ0+5oNMqR2XzRQ=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/el.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			WByDFpOs3sg2GEzut4FZNxWtUdWYhpGMksTtxby9xNo=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/en.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			vJtPQ6QanqatlH7tuzoVEOYV9R6fXP52MPVsH6KdN98=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/es.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			bXoC+7lQCox5XxzHaZgERO5YGNGiSiW5OJ010B3vFzM=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/et.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			EFDeRuURyzGBe6wJb7KY00N5X8q6ZfCis4p14DMZvF0=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/fa-ir.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			T7aDi8C9NUdM209r1ELwYzL8HE6Tblg7TDsEB8v0zGs=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/fi.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			cOz8Xcw/VOQKpoDfHNAoIkMNAtV74M+LjUUXw02hywk=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/fr.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			7auT87uNYUV8HAfSBTFet3Daje0/zWa0wJo0H2+yAzk=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/he.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nI3dv482dtlP490ooEGPlRAAIhbbDkN0ZP4Ku4TCjoA=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/hi.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			mWwrNrIEIRKk4C2C9FfMd2OGcHsoF14Q1rPibmMh9+8=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/hr.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			YSDx9FPSqfyoDmju8GeJWzQhLfNkUPQ/RduehKguGvw=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/hu.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			9iO1GJwCPdEACPaTj+f4D0ZSVJwCBsaf8ugEVyQp78M=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/id.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3ka16BDTTBPg5yQE5DNUrACVmEesZaOsYAoyKD56JrI=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/it.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			kHvX7aJDJCnV7egqOo1kYZ9GusjL+MecdJXsO3OC+mM=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/ja.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ABMyFAptTntKcR8qb7kvfGV88MKwJbzEO4LS/b0IQ6Q=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/ko.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Zw1X1GooCUjgRdqIpYOEUEg6CJqg2G3A+bK+1yPM1FA=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/lt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			u6eO5GgLwlBlPDzoL943ofyyZG84HRH4Mc2AjSQcZzI=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/lv.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			2mwshOIDy18H6INpT2HbFPaLhGaSQwxHsBdug1V5MjQ=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/mn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			O4wSQwZD4Cec7/sHzvOkuprE/0n7CoQhnQMepn0x6D0=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/ms.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			uqVVhFUaS8PeO4PBWwlMeSnXWhxj7ZHmxWoYqGLHSUY=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/nb.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			emcNKAGIR/PDF+PevHn70erPR8G9GQi1PiQZHNNibXk=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/nl.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			apl3ShLbg7W+oWR770XGS+14p30+M95vg6HSI5ySvDE=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/pl.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			7JO3lCDzYf/zUcotR5NUIR4W5aF7/dQ30klXyK0wTTY=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/pt-br.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Gb1lMIN1z115Hl4HpCwDZOM1ji40uHt/e3uVJNMEA20=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/pt-pt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			KTp9pptnUP+H4gn33NMB+oqz9wi/3kw8GA6eekpQTvk=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/ro.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ysgZjkPU0325SInRySPttiOrslXImIXpWWDhvKAniSc=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/ru.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Jdeb8HesQbgz3WzakL1/Fuo23lrh5PBr6HBK7Fg3L/Q=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/sk.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			GwYBP7KCl6zILdq2PY1avKfKkxRiuYpR0SPvWlWyJtk=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/sl.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			lFDmWO8Q+LUWPIsKeG5YNm7PdObmuO3Ob7aiSHJ7vG0=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/sr.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			VNL4oJH9JDSgUkiuqWwFOM9f3XXV2XPmFHUVX2kczPY=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/sv.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			GVuvtbK2SLJDVWxqXoHrOGxP9fDKGj5BY6a39NRBxpc=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/sw.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			vV7hs8a75bM85WIBnM0yyKdI5GhR0dckFmX4HBC4j9Y=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/th.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HR308XeXDDXmGs/0tt6fcPkzIvNO7HJKoklSFybjzYg=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/tr.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			QnoZGC4nlCtiDREwYxjkEgDlIOVU9lMbNmnJHLMZU0w=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/uk.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			fYOrIWhRqrETPrsYtQxJ9orsv+Wh/zEULkSBzDglCnE=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/vi.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			sWikea2RYntX0zxY5+cHARuaHqa8gh2vTCSuMMT8YyU=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/zh-hans.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Q2ZSOxUpVWoHYEPmY3pVx/VB1tq4zVYD7TXrYNeWTe0=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/IntercomTranslations.bundle/zh-hant.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			XbTW3t2ymTduemWBngZjYsK5Wkasqh7+99R7kkdGIWY=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/PINCache.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			bHK5menKAOvlzfVs0XitdKr++jWeImGTuk4/oLjnads=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/PINCache.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			VQPci+mzt51D4Gkzdhclc7ew69EyAAH58tMJ/0yFvo0=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/PINOperation.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			MJKXj79e1rfngaOLn8Evmx4+LwrY3Rvg4ar6RFHipVw=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/PINOperation.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			Uh6274Qwdz5cAQ4YOP6d2PpdYre3bRzqjX2NqtyxROI=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/PINRemoteImage.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SsrVHx6p/T4AqYqWTo3Hp6Isgoxmv/5a74T8Zh2j97M=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/PINRemoteImage.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			Q2PwiGNBoPErTuA3CfXa/xo/rRxLQDKhiAd3G7PBJLc=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			ylFRif+2jEnaxLSgp8YgKlyTgMmcAmuoT2eW/7zZE6w=
			</data>
		</dict>
		<key>Frameworks/Intercom.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			3dKgRugsK+sZQJEQvt0Zrm4qIFTFUlO/ga8hee09fZE=
			</data>
		</dict>
		<key>Frameworks/OpusKit.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			0LtSC3irWuT5Xb6cOpf7K9STow1b6SaA7eUhmDha4SY=
			</data>
		</dict>
		<key>Frameworks/OpusKit.framework/OpusKit</key>
		<dict>
			<key>hash2</key>
			<data>
			1z9I/J0RTlJ0H6ZtPFsBkqNEqRqKc395n626a50qcEg=
			</data>
		</dict>
		<key>Frameworks/OpusKit.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			25KG/MxTurvtOy24weTTLaGNfKqk04QiL/YAllleLQM=
			</data>
		</dict>
		<key>GTMSessionFetcher_Core_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			xgXvK24tPX3LreyDd3k5HRP6S2SHahHpnMBweWSbca4=
			</data>
		</dict>
		<key>GTMSessionFetcher_Core_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			PkqTy+hqzvfdfgY6KMhJmS9Vbn9SytxfN8HosOG1RoY=
			</data>
		</dict>
		<key>GoogleDataTransport_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ydEO1l73yPtI+W4i3CaPHFBNgcrXqT/lXBGQ6YbuKcs=
			</data>
		</dict>
		<key>GoogleDataTransport_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			z7s8T3ambVNpi66R9xEMAPIUjm5vE619MlkpCbwBDlE=
			</data>
		</dict>
		<key>GoogleService-Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			XU6M86CM/mwDnFsJS6seAWfyHjLJQwgQ+f9PUMRk8dI=
			</data>
		</dict>
		<key>GoogleSignIn.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			WOPoaCYKTg34dnmCzMB8iAH/cpcJv7BBwkEIayVS0YE=
			</data>
		</dict>
		<key>GoogleSignIn.bundle/Roboto-Bold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			WU10pJ4we+fMnh7l8QI2hOaCDPEbzJaL7lkDkeGtWlo=
			</data>
		</dict>
		<key>GoogleSignIn.bundle/ar.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ss+ejZ/IrQ+WELPlTvC4tJTwIbf6I6zvYJSyIFq+jHI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/ca.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			M+iP9QDcPbJGLirJM8EcKJKfrTUfV7Pm1irERhCtYao=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/cs.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			UdoIYkwqed5HyBaDsp6KE0SkeIdWO+JW4ad6Z/a+NUQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/da.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			6CYblkbY6on3v3phpzrtzjD+UfaKqUfsvf6Mx+BNt9o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/de.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Fs1BPfGrd+UJ33b4c6Wmh//PNgsfFQdD2TTFqzN3wig=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/el.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nMDBNJz2Zfx3MzDTGiYo9gCkPKqLXIgLA0pWZ/jw+rw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/en.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xyajnDguPxp8H1js9HD4EtomN0n5MNqH98DDqg7GSMI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/en_GB.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			or14KgUbGgs8Q37Gox6b9/Z3iZWnKswyoOtKY5mtGsI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/es.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			UB5ExPO7nAWxMpfGAkD9Dhbnn4pj5vslGYII3LlCHKY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/es_MX.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			hNMnxxl7flSaHp6Ni7PhywLgKCxjI+X7Pd4KRzla9jQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/fi.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			DpnzGonq2MwPxyrWq9zISr0m9pkmXu/2GoibNznadzs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/fr.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			tVE4d6zy15yPDq/3/N5KoWXzq7AWEbYGESYC/UQ3d7o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/fr_CA.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			9xCSzz/bp+PgPQ2ZdIN4sRGUfKd6mRgw3dJMOZIrCG4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/google.png</key>
		<dict>
			<key>hash2</key>
			<data>
			KZYAxsP5lgvGgLduEjF901DWOgDsv13keDEb2UPphvY=
			</data>
		</dict>
		<key>GoogleSignIn.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			YIMFDG4vd9yXjg4T1wcAtDC7Vreja1pcgKJzOsZgcqY=
			</data>
		</dict>
		<key>GoogleSignIn.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			VqPZ1AQqGKWVpnftZGTeoQXx6Noy4Hw8XIQErzqqh4w=
			</data>
		</dict>
		<key>GoogleSignIn.bundle/he.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			KtB1FmX6YQB2EQfECgjfBJBnQA7X2NP7QuPuxFZ6YLA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/hi.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3UHXJzarX0qX0J3bFMSADuQLm+0jadZPDF/6uWpiRh4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/hr.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			JA+ymYzp46eWqXkG/G+WClYRwHhNxzsWyukU+u0KW/4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/hu.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			GoRF+Zxo+7BfNxT4D3DtCIpiNxtbmIXjErRRbwfYnk8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/id.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			PyRv43MvgTyToElSNlECqWWiJIriTlBPGVMTmFiLK/k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/it.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			TtxpfPqTYA6LaQTYcihANVBXvLLpxiQmqXzstkL2M8g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/ja.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			hk3OYj6xqZvup+cqHbkAhhDEA8l1hXBMH3gLIIk4ERQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/ko.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			AhqCmUXH5He26Jp82dpoP1opwP/AwBLug5uvUO96wBM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/ms.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			y2wRSAv0XEuZrBwtz9tnpfgqeZkXomMNT7PpYTV7XJM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/nb.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			AqDORhPvDCPsPxYdWdXEk+neYmyBsEvpMYChGkLK6rA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/nl.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			SQUYZcAGQO3tTDWbHoIOEdHxkQVdu0gFkfk5fTerXg0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/pl.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			zB70RS/MmKy+7SKVC9fwoipXngJaj1DHj9u2AO045V0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/pt.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			h4m5LhoUwf1F8mYZJzxXPRsQnjz0AOVugKWHC6rrzO4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/pt_BR.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			h4m5LhoUwf1F8mYZJzxXPRsQnjz0AOVugKWHC6rrzO4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/pt_PT.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			VgBP67jGl9/ChEtJOy0f2pT31xiBxen0V91QtDRgDpk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/ro.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			r4dFHw1YRkncNtW9F0rb86p6SQ53QicvL33YbiJi490=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/ru.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HTeXAqlR7PmIQ9lXCSqMSv7j7v6GamABZD47d3Vdxdw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/sk.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			P/cihlo8LmENm4TLBV2TYyC4PWbK+iTI64GTaNQdXaw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/sv.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			EB6TqcbOigHnqGM3qiEvaIEEVBHW5FJwF5/vJhhhQx4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/th.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			87AIPzi3JhCduSGnkEqY3kpQF7l4xbCahIDFRHo8T8c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/tr.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			DUnrVChJprfGiaclYvcgTKdFHOG+K+qtB0NSDZurgNI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/uk.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Jhxu6B2f/rMv7vqp4r9JDKbb+lbniEMqlbSjM+LHru8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/vi.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			XdgQI6jed2yICWt5Lgc2Nmjlld6tS+jlIZu73Yg5j+A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/zh_CN.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			UnRDQSf3cJY9kq+HAKK4OMrkkbt2CGQtT/KlF/uB1nc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn.bundle/zh_TW.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8K9LpcAznoYMsbk4Z5+PTNH5EEAbvqGHz7sjmrE7t+U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleUtilities_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Fn4R9MoDHHEPG+2OGSllsWe7vYRCEvSucZNdQLiPj5I=
			</data>
		</dict>
		<key>GoogleUtilities_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			+Btc+PBDZicS7KnpeFdnJkzxkAJf5720l3cpbAaN5Tw=
			</data>
		</dict>
		<key>Mixpanel.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			uJyiTf4pR8pAvdYzzj8RJImwEtsuu/pan+iAfBc8GSc=
			</data>
		</dict>
		<key>Mixpanel.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			Sc9JmUML/ZuMAMVfSL+0xFE828rkwaMb15Z1zRkLXc8=
			</data>
		</dict>
		<key>NordicDFUPrivacyInfo.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			UgPBXsYILcLG0nWFfyd6RLLhHWsL+P3jS2Z92oxGNjM=
			</data>
		</dict>
		<key>NordicDFUPrivacyInfo.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			9yZ2JAedHyxV3xaCSZC+Z3eaYd3RxyGytPtQFfD3zr0=
			</data>
		</dict>
		<key>PostHog.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			lzzdSJy3Chx5Q3y1wM/ju/yaSseZje3M6tSVkfjtg6o=
			</data>
		</dict>
		<key>PostHog.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			ThroJG4j7f39WdC+6WBpRWJsEMg+Vaf1rz8E3yX6db0=
			</data>
		</dict>
		<key>PostHogFlutter.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			c4v7tFwakE5fTVd2BUmxvuBjI52StRsoFVu1JSSzZxg=
			</data>
		</dict>
		<key>PostHogFlutter.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			ThroJG4j7f39WdC+6WBpRWJsEMg+Vaf1rz8E3yX6db0=
			</data>
		</dict>
		<key>SDWebImage.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			H4JdbXCm1Qx7ppHtbnEZiHl0G7dQBvKbXB5E94WoJiA=
			</data>
		</dict>
		<key>SDWebImage.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			A7LHCDOjMaKx79Ef8WjtAqjq39Xn0fvzDuzHUJpK6kc=
			</data>
		</dict>
		<key>SwiftProtobuf.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HA5aG2otU6Wlz9sl0mnz+0rsMNhmMpl4PKh98dgLRB8=
			</data>
		</dict>
		<key>SwiftProtobuf.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			oHQY5f4SjiJM03lkvCHFA7eqL1dee0knRoR4rYcyFP4=
			</data>
		</dict>
		<key>SwiftyGif.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			pWLkQpZy13UGy+GHQ1gc1tH9pAHi9ufLiO3el2lEUnI=
			</data>
		</dict>
		<key>SwiftyGif.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			jWXQtDoSYs/inoM5bACz25T7Eg1E0RiNknNB8UpsIjw=
			</data>
		</dict>
		<key>ZIPFoundation_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			biuUNGLyPr8/wFwGjk6aWBctQk485GQzOAaby2Gp+1I=
			</data>
		</dict>
		<key>ZIPFoundation_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			STi9dgvtDgLwdXtE2Tjno9fIKL6pJ8E3XGWwqIdzthk=
			</data>
		</dict>
		<key>app_links_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			b5B3gibhzmL2HlcF4PH+/R4eCBcXI3SO6ZdeH+9z9h4=
			</data>
		</dict>
		<key>app_links_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			3wG/jzEbezUNAdVa/lBZ6tm6KwBpxUIeadm0BffKvqI=
			</data>
		</dict>
		<key>connectivity_plus_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			BxFY/dMKmsICq3MrGFmMSUXC4cXMii3cuNjeeJ9JlcE=
			</data>
		</dict>
		<key>connectivity_plus_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			r37hmxjkxWvCGOnCNoJHLx1R5QP2tr6dFpvIxJl31q4=
			</data>
		</dict>
		<key>devAppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			y/OfanWo/ADG6/HfRw54/gwW6Bq0U1Q4NABn8S3X9bA=
			</data>
		</dict>
		<key>devDebug.xcconfig</key>
		<dict>
			<key>hash2</key>
			<data>
			aqGMnrkmQbv4TCr30oOcdgsr+7T+QHUS5pkfTiW+iGQ=
			</data>
		</dict>
		<key>devLaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			uDJiJiHpGEGT9Wz8ZxCtBwFjn7KfDAzDxs8GMKcfIzU=
			</data>
		</dict>
		<key>devLaunchScreen.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HyVdXMU7Ux4/KalAao30mpWOK/lEPT4gvYN09wf31cg=
			</data>
		</dict>
		<key>devLaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			VPNjf2cf66XxnoLsT0p/tEi7PPwPsYDwiapXH8jwU+I=
			</data>
		</dict>
		<key>devProfile.xcconfig</key>
		<dict>
			<key>hash2</key>
			<data>
			86ZyaEwFXNCC2quQtS/2Nazu78Q61dNj9Rl6b53UY5E=
			</data>
		</dict>
		<key>devRelease.xcconfig</key>
		<dict>
			<key>hash2</key>
			<data>
			86ZyaEwFXNCC2quQtS/2Nazu78Q61dNj9Rl6b53UY5E=
			</data>
		</dict>
		<key>device_info_plus_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			JPE0uxtdU0OgSACHPO0UyRD5AyQIYZTfUxgixZBEeJQ=
			</data>
		</dict>
		<key>device_info_plus_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>file_picker_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			FO2i9veu4AvQVr9vB+r6hmCod+70eM3OTLdNvSOK6Hk=
			</data>
		</dict>
		<key>file_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			RyJqKWCN8gatChEOav61p3/1dawd+cdr/bLW37P6/tE=
			</data>
		</dict>
		<key>firebase_messaging_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			n/VlPQNBZ03MsGvdGSeWiR1zRT1Hth+nzYcochCWklQ=
			</data>
		</dict>
		<key>flutter_native_splash_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Yn8qa/Nv2xN7TtdKWOrXraFgCpwW14F+ZDyC8fP4X/M=
			</data>
		</dict>
		<key>flutter_native_splash_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>geolocator_apple_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			e4clxXlmEFlA33jKE+nJK0EpB+guF0E0PEqdvN0Wr/o=
			</data>
		</dict>
		<key>geolocator_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			p+c+xOFN/pYr5bFknUH/y01YGEb8+JJchzbtJ60mdTI=
			</data>
		</dict>
		<key>google_sign_in_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			/ucFbxcctYjH9zlPwGcezNS5WSBG2rCdfetm0NzkCNY=
			</data>
		</dict>
		<key>google_sign_in_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>image_picker_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			GZI5orYd/2aemUGIbYPFkGt3YdMojW05nNz2LJfkf3I=
			</data>
		</dict>
		<key>image_picker_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>in_app_review_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			+rNQzNWVJXTB9c+laXp0N0NxvTGOw838TivbI36DYqk=
			</data>
		</dict>
		<key>in_app_review_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>map_launcher_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			5w5Klde01/qYDbK+vlvp6u+3Xrz2EjtvYj4j8KNEto8=
			</data>
		</dict>
		<key>map_launcher_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			p+c+xOFN/pYr5bFknUH/y01YGEb8+JJchzbtJ60mdTI=
			</data>
		</dict>
		<key>nanopb_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			/RU4YsRIcqR/twBAn1S5J2p3WB0jZDDaqBTB72tXaZw=
			</data>
		</dict>
		<key>nanopb_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			cpujy9D0WMeM1h7fFzUO2v4ONMqG4xTsZMjLIszSG1Q=
			</data>
		</dict>
		<key>package_info_plus_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			FWy29sif3PknUsTebdx2aZIToxWwsCugAwPRmyZcoAE=
			</data>
		</dict>
		<key>package_info_plus_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>path_provider_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			pnlB4nmlw0MH4q8erNhW6xTTKPT9e0akGlvd96ghyLw=
			</data>
		</dict>
		<key>path_provider_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>permission_handler_apple_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Dcglu+Uii3x7a94pg96Ptv7b5rVqK+aMGbknmvsXPc0=
			</data>
		</dict>
		<key>permission_handler_apple_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			ETZWiZY6EZHpaiLgs59i8FuG0NJKvoBAXBpc7vCamxs=
			</data>
		</dict>
		<key>prodDebug.xcconfig</key>
		<dict>
			<key>hash2</key>
			<data>
			vcECSE0gt9zAVTGbkn72Yconc35mvcsZ0OXY7cJAkFQ=
			</data>
		</dict>
		<key>prodLaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			rWotSUk6J4hfbLkYzrYEHltz0JAd2WSxKAszVMBB5EQ=
			</data>
		</dict>
		<key>prodLaunchScreen.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HyVdXMU7Ux4/KalAao30mpWOK/lEPT4gvYN09wf31cg=
			</data>
		</dict>
		<key>prodLaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			VPNjf2cf66XxnoLsT0p/tEi7PPwPsYDwiapXH8jwU+I=
			</data>
		</dict>
		<key>prodProfile.xcconfig</key>
		<dict>
			<key>hash2</key>
			<data>
			3w7CMvfdKmsitm/DsXfu+v3CcsPOVp4ANR82rvgejL4=
			</data>
		</dict>
		<key>prodRelease.xcconfig</key>
		<dict>
			<key>hash2</key>
			<data>
			3w7CMvfdKmsitm/DsXfu+v3CcsPOVp4ANR82rvgejL4=
			</data>
		</dict>
		<key>share_plus_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			aQyfa/o3OZ24aZHUAwlKerbm30R4UZe923jejwxaHSc=
			</data>
		</dict>
		<key>share_plus_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			fymg33dvK/CeS+IVoUlKAB0y0lEfHOBnF3U4uwxl7lk=
			</data>
		</dict>
		<key>shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			BWQouTi9VwGKYAGdFcPB7i+nJ/I2h1mLu9k0hIsYCxo=
			</data>
		</dict>
		<key>sqflite_darwin_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			7h1yLtahH2NY2u9VJmACTrvn5lql/mcVCbtzDdLCVOk=
			</data>
		</dict>
		<key>sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			RyJqKWCN8gatChEOav61p3/1dawd+cdr/bLW37P6/tE=
			</data>
		</dict>
		<key>url_launcher_ios_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Ke5ApCTZTjc9diOuboucxnau1zX32xg18eerV/p8G2c=
			</data>
		</dict>
		<key>url_launcher_ios_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>video_player_avfoundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			QY9rbcIZKvf7NzhIc1+vFJFEaMbUbEMtU0p35gWddxQ=
			</data>
		</dict>
		<key>video_player_avfoundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
		<key>webview_flutter_wkwebview_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			srVqTB1zAwlx6ibttJcN35pfNyvCvB1EteHGu26njGc=
			</data>
		</dict>
		<key>webview_flutter_wkwebview_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			bS2g2NkwIn1CjB2TY7CtbjoS4sm2jFzilxWKdBL8jDE=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
