<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Assets.car</key>
		<data>
		yNxLEDaNhgC6Fs5UFW9DZQjRRPg=
		</data>
		<key>Config.plist</key>
		<data>
		zmV6UqBSo6r1NOz798vd5O4zTBA=
		</data>
		<key>FID.js</key>
		<data>
		z4jnPaq0vybyhdKOJHGNOhsfjOI=
		</data>
		<key>IBGActionSheetCell.nib</key>
		<data>
		28sb+YQa/IPAucVC0/5EhX0C3yU=
		</data>
		<key>IBGActionSheetVC-iPhone.nib</key>
		<data>
		ROk78ne0CoWErdr4DqIblXFBgCs=
		</data>
		<key>IBGBugVC-iPhone.nib</key>
		<data>
		BRTF/vr48N6G/Yjy71Fn2t9WvwI=
		</data>
		<key>IBGChatCell.nib</key>
		<data>
		WVeMKpeYhkGI12NHPasDHWKaoHc=
		</data>
		<key>IBGChatListVC-iPhone.nib</key>
		<data>
		pF3XyuGTCgJGMDLibTcL2E/mgCo=
		</data>
		<key>IBGChatVC-iPhone.nib</key>
		<data>
		/GiKM60W5tqdFUqpcBmtMNLWmqM=
		</data>
		<key>IBGFullScreenImageViewController.nib</key>
		<data>
		8GJaXZ+I7Lmf5RO+zSm9plZ2JUI=
		</data>
		<key>IBGPoweredByView.nib</key>
		<data>
		+DD2unnopUvmqWJed3GdjNuuNGs=
		</data>
		<key>IBGPromptCell.nib</key>
		<data>
		nqKBFtxE1hjUh3sh0N6s2i0SS7c=
		</data>
		<key>IBGReplyView-iPhone.nib</key>
		<data>
		cSRWm2Iq5luooHMeWKaRdK9Pgv8=
		</data>
		<key>IBGReportCategoryCell.nib</key>
		<data>
		Fob6Su5czfC8QJh+4TgZQkNZHMc=
		</data>
		<key>IBGScreenshotVC-iPhone.nib</key>
		<data>
		9pRZEj/o9lwzhptHN/Zn+9jRkR4=
		</data>
		<key>IBGVoiceNoteRecordingViewController-iPhone.nib</key>
		<data>
		mlR6U8rfPFOZmkYgKGjE7ylpyRI=
		</data>
		<key>InstabugDaignosticsDataModel.momd/InstabugDaignosticsDataModel.mom</key>
		<data>
		c2pR/gLEza/sKbyK2rtbn1BK8xk=
		</data>
		<key>InstabugDaignosticsDataModel.momd/VersionInfo.plist</key>
		<data>
		il5a0h2jo6O/snHjiqgp8oL2lRw=
		</data>
		<key>InstabugDataModel.momd/InstabugDataModel.mom</key>
		<data>
		9Zr8RSPZT5ysMog+P4oUP6kArL0=
		</data>
		<key>InstabugDataModel.momd/InstabugDataModel.omo</key>
		<data>
		T2K7DIGI0VFPh+z+Yj/nT9aL3m0=
		</data>
		<key>InstabugDataModel.momd/VersionInfo.plist</key>
		<data>
		ltfiFuSHIPLDjz4MsmYBFAHJ8Cw=
		</data>
		<key>InstabugSurveysDataModel.momd/InstabugSurveysDataModel.mom</key>
		<data>
		oBkPjmoJJxDcBQQIbV+9F3eCbaA=
		</data>
		<key>InstabugSurveysDataModel.momd/VersionInfo.plist</key>
		<data>
		cIwOQKk81m/mJaP7KNNNIp68dI0=
		</data>
		<key>InstabugUserStepsDataModel.momd/UserStepsDataModel.mom</key>
		<data>
		ZHgm6fw3OEMg8Q7jjmW90gmPyd4=
		</data>
		<key>InstabugUserStepsDataModel.momd/VersionInfo.plist</key>
		<data>
		gJ90TbeGmu3KzpSL0SA5AtqMP/I=
		</data>
		<key>Instabug_dsym_upload.sh</key>
		<data>
		DP8KwyTvy5H7hSnVlrDff4kEb/s=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		qIzgfWEjw69oe/+vqj0TUajvbnM=
		</data>
		<key>UserAttributesDataModel.momd/UserAttributesDataModel.mom</key>
		<data>
		/vX96ROZc8342A2HBhNr391nb7Y=
		</data>
		<key>UserAttributesDataModel.momd/VersionInfo.plist</key>
		<data>
		+BLJJZUtOfRJP/ZD/ItEEfdmEu8=
		</data>
		<key>ar.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WGWKsm0lr0i+xCHI3iI6qgGQUGc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>az.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			I5I2sMxFN6VpkLlxo2awAMDkpQc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ca-ES.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			n1t9rsKSA/Qggp8b4YDmuYfgBUE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ca.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			atokjgvUP1Vmk9gdy1FVd/0SWOw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cs.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7vpjV0K1NCsK1triQSGsKuyHQYo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>da.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kdTabL7H2HnMKbiU1Ypr9YWn+SU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OmO6yBOPEjC40Ns1cyLfJqMHnRc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			E7vzp81spzp/OVcH3QMTbzZd9m0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			JT8QT5SebrK2XFFs3Iov80e9WkI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			2SNBjq0z1PBctZSiG95PJo1r4rA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XvkNwTMy/L9Ul2FJuIFsdXb6tn0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nb8AUSeVgi8iZZJtCZTM9Wnd1dY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hu.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hu6dFQI5ARjgSZLw1ijXAuO1hJU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			b11yeT46Xu01huDf78GnDPTHFbQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6H7j42CvSdaI1R1Zej6RWtwMkto=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			I1wNK6mkWxnD8BPtLLbdQKyDyr0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nb.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3nEYtISxR6bmu97Op52Os+biWYM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9Ul1WgexQcR1U8skkMoEbV+qsTc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/89hLMCQZUJU4t5D6d4omWX5cGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-BR.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jPZtLy0k6hzRSC/2U3af6/kD1HE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-PT.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vv2UhgZvnDlpsIXk/6Tm4jcTGE4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ro.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3DHKaLWL+U6mJr3ZtkrZsMckbMg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vPyLdPpxI/agU4aWWK8NDs9eb70=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			JfWK424Bgf9OTJfyFNbMKu3/pGo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>strip-frameworks.sh</key>
		<data>
		YOS7aGxjvvRtsNAQCXrKoe5nT7w=
		</data>
		<key>sv.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			46AlVm227dP6G6m6puFihDwfcZ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			leqXZMjnJYU4eKtP8ezzA3gDLqs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WBqtayk6qOeHjZtkPmpf7BQPMyQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hant-TW.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vdr/3isGcsjy1OxQgNV5QWcDKNs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/4eBCWIJ9N0zXhm4ucKfovpyFUM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			yNxLEDaNhgC6Fs5UFW9DZQjRRPg=
			</data>
			<key>hash2</key>
			<data>
			T/T45EUEgmeXnFCSblpf+IAYcrvPKIrHF7JcJPm2rxo=
			</data>
		</dict>
		<key>Config.plist</key>
		<dict>
			<key>hash</key>
			<data>
			zmV6UqBSo6r1NOz798vd5O4zTBA=
			</data>
			<key>hash2</key>
			<data>
			kmHsztpgjvF0JW5f3HdMHm49z1M0CcG8OT1JDQHHE/E=
			</data>
		</dict>
		<key>FID.js</key>
		<dict>
			<key>hash</key>
			<data>
			z4jnPaq0vybyhdKOJHGNOhsfjOI=
			</data>
			<key>hash2</key>
			<data>
			7CN8uNWQfSxqec5gNXAJcCBJlxpSBFzdOOr09UKUr4A=
			</data>
		</dict>
		<key>IBGActionSheetCell.nib</key>
		<dict>
			<key>hash</key>
			<data>
			28sb+YQa/IPAucVC0/5EhX0C3yU=
			</data>
			<key>hash2</key>
			<data>
			Iig4a4WB2008joZTlxE4U8C2/D6UYpTL1IHfCFEhl6Q=
			</data>
		</dict>
		<key>IBGActionSheetVC-iPhone.nib</key>
		<dict>
			<key>hash</key>
			<data>
			ROk78ne0CoWErdr4DqIblXFBgCs=
			</data>
			<key>hash2</key>
			<data>
			thnwjsppFU3uFNWb87aG5QV9vxfYCJCnvSTMqqH3mY8=
			</data>
		</dict>
		<key>IBGBugVC-iPhone.nib</key>
		<dict>
			<key>hash</key>
			<data>
			BRTF/vr48N6G/Yjy71Fn2t9WvwI=
			</data>
			<key>hash2</key>
			<data>
			0InfsO+5kxbkIxuwHIVcdJoBOxs4kbYlhGMK9ylZNfk=
			</data>
		</dict>
		<key>IBGChatCell.nib</key>
		<dict>
			<key>hash</key>
			<data>
			WVeMKpeYhkGI12NHPasDHWKaoHc=
			</data>
			<key>hash2</key>
			<data>
			21yi/S8NG+ny7prdqeNM2VJPkjr1NhE3/5o2v7ZAxOg=
			</data>
		</dict>
		<key>IBGChatListVC-iPhone.nib</key>
		<dict>
			<key>hash</key>
			<data>
			pF3XyuGTCgJGMDLibTcL2E/mgCo=
			</data>
			<key>hash2</key>
			<data>
			yaVIgHjxEwp8Uy2xpbDBjjigcZnK8iRr+VaYrtJgc5c=
			</data>
		</dict>
		<key>IBGChatVC-iPhone.nib</key>
		<dict>
			<key>hash</key>
			<data>
			/GiKM60W5tqdFUqpcBmtMNLWmqM=
			</data>
			<key>hash2</key>
			<data>
			/2B9tUtc6S5JVMCUUsPIkgRZDg9B1aobD92AEa6KU4g=
			</data>
		</dict>
		<key>IBGFullScreenImageViewController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			8GJaXZ+I7Lmf5RO+zSm9plZ2JUI=
			</data>
			<key>hash2</key>
			<data>
			QKpva1AeF/u6qmCYJIbn0Y6Go8ogufaUhEarNf0lXuE=
			</data>
		</dict>
		<key>IBGPoweredByView.nib</key>
		<dict>
			<key>hash</key>
			<data>
			+DD2unnopUvmqWJed3GdjNuuNGs=
			</data>
			<key>hash2</key>
			<data>
			qjunAvW3geVx2RX/1xTre1VOycH8p/BxMtI3Ph9qYfM=
			</data>
		</dict>
		<key>IBGPromptCell.nib</key>
		<dict>
			<key>hash</key>
			<data>
			nqKBFtxE1hjUh3sh0N6s2i0SS7c=
			</data>
			<key>hash2</key>
			<data>
			4ZJxe77m+BW5WXuPJ9f2NpOIr3SRLHZ7ETT7vDD0rlI=
			</data>
		</dict>
		<key>IBGReplyView-iPhone.nib</key>
		<dict>
			<key>hash</key>
			<data>
			cSRWm2Iq5luooHMeWKaRdK9Pgv8=
			</data>
			<key>hash2</key>
			<data>
			KjkXxkFVhFvKH06+9yL7I4lDDikndg6CuBYaTj6YcDM=
			</data>
		</dict>
		<key>IBGReportCategoryCell.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Fob6Su5czfC8QJh+4TgZQkNZHMc=
			</data>
			<key>hash2</key>
			<data>
			x5zIX14wMBBOXTkaTR7yEo7Y6NF4bacNST/lxv0HVhA=
			</data>
		</dict>
		<key>IBGScreenshotVC-iPhone.nib</key>
		<dict>
			<key>hash</key>
			<data>
			9pRZEj/o9lwzhptHN/Zn+9jRkR4=
			</data>
			<key>hash2</key>
			<data>
			2QQZVP+Su6+Sp5oH0dx0RdVCOikX33H9HS/n8b833CU=
			</data>
		</dict>
		<key>IBGVoiceNoteRecordingViewController-iPhone.nib</key>
		<dict>
			<key>hash</key>
			<data>
			mlR6U8rfPFOZmkYgKGjE7ylpyRI=
			</data>
			<key>hash2</key>
			<data>
			uU6wlsxRj2H9e8fKPmPvi+5nu7l34qG+KuXOk22HwwA=
			</data>
		</dict>
		<key>InstabugDaignosticsDataModel.momd/InstabugDaignosticsDataModel.mom</key>
		<dict>
			<key>hash</key>
			<data>
			c2pR/gLEza/sKbyK2rtbn1BK8xk=
			</data>
			<key>hash2</key>
			<data>
			P3jGB4b8T5QNWHILpxgyXBLiF0GBxZmDYE4eKyqlTqw=
			</data>
		</dict>
		<key>InstabugDaignosticsDataModel.momd/VersionInfo.plist</key>
		<dict>
			<key>hash</key>
			<data>
			il5a0h2jo6O/snHjiqgp8oL2lRw=
			</data>
			<key>hash2</key>
			<data>
			qic+OO5WHhsPRqqyKJ9hLObsahmhX39YKLFkoABZ6eo=
			</data>
		</dict>
		<key>InstabugDataModel.momd/InstabugDataModel.mom</key>
		<dict>
			<key>hash</key>
			<data>
			9Zr8RSPZT5ysMog+P4oUP6kArL0=
			</data>
			<key>hash2</key>
			<data>
			NuesdTYd3gmemqURPnhWsDxIoERhqPR5//ZRdCOS9ck=
			</data>
		</dict>
		<key>InstabugDataModel.momd/InstabugDataModel.omo</key>
		<dict>
			<key>hash</key>
			<data>
			T2K7DIGI0VFPh+z+Yj/nT9aL3m0=
			</data>
			<key>hash2</key>
			<data>
			G4cpo3+HsXx3tEwTM15mYQHXjPCw3vPzWf6Mcc8Yb9s=
			</data>
		</dict>
		<key>InstabugDataModel.momd/VersionInfo.plist</key>
		<dict>
			<key>hash</key>
			<data>
			ltfiFuSHIPLDjz4MsmYBFAHJ8Cw=
			</data>
			<key>hash2</key>
			<data>
			mvNWWyKAafIKbofs4NB4wTNjE7z93MWzeA9zj0JZAe4=
			</data>
		</dict>
		<key>InstabugSurveysDataModel.momd/InstabugSurveysDataModel.mom</key>
		<dict>
			<key>hash</key>
			<data>
			oBkPjmoJJxDcBQQIbV+9F3eCbaA=
			</data>
			<key>hash2</key>
			<data>
			ZZwLVUd0OYoN1BojiKO/ep0OnKnqcRy9ULwoHCJNoj4=
			</data>
		</dict>
		<key>InstabugSurveysDataModel.momd/VersionInfo.plist</key>
		<dict>
			<key>hash</key>
			<data>
			cIwOQKk81m/mJaP7KNNNIp68dI0=
			</data>
			<key>hash2</key>
			<data>
			Y8N2vW5ZuhzAXAho3Y/zBZ0PI5g5i80inwV9dJYx8Hk=
			</data>
		</dict>
		<key>InstabugUserStepsDataModel.momd/UserStepsDataModel.mom</key>
		<dict>
			<key>hash</key>
			<data>
			ZHgm6fw3OEMg8Q7jjmW90gmPyd4=
			</data>
			<key>hash2</key>
			<data>
			J2urkE8keJVImAwVKZWm2jUMqmcqmghLWqANsm/nwAs=
			</data>
		</dict>
		<key>InstabugUserStepsDataModel.momd/VersionInfo.plist</key>
		<dict>
			<key>hash</key>
			<data>
			gJ90TbeGmu3KzpSL0SA5AtqMP/I=
			</data>
			<key>hash2</key>
			<data>
			lOis0MDxpoguZJnMXEhmugJ5A5GLG/gYzZ1jjL2e6dg=
			</data>
		</dict>
		<key>Instabug_dsym_upload.sh</key>
		<dict>
			<key>hash</key>
			<data>
			DP8KwyTvy5H7hSnVlrDff4kEb/s=
			</data>
			<key>hash2</key>
			<data>
			yQRp47oMhb41/QfLIxifnPWTqKi+hpJq0v7fyqri7JE=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			qIzgfWEjw69oe/+vqj0TUajvbnM=
			</data>
			<key>hash2</key>
			<data>
			68XwNvROhusgvn9QwENIhXOLAT0MiwXvdYbHK+//gkE=
			</data>
		</dict>
		<key>UserAttributesDataModel.momd/UserAttributesDataModel.mom</key>
		<dict>
			<key>hash</key>
			<data>
			/vX96ROZc8342A2HBhNr391nb7Y=
			</data>
			<key>hash2</key>
			<data>
			cRqSKwCv9gVnEJeQyAh/5b7rI4Q4nCkme83G+NsfC4I=
			</data>
		</dict>
		<key>UserAttributesDataModel.momd/VersionInfo.plist</key>
		<dict>
			<key>hash</key>
			<data>
			+BLJJZUtOfRJP/ZD/ItEEfdmEu8=
			</data>
			<key>hash2</key>
			<data>
			5lcpESUTF7dVvryJcaa9Lmcwj5cBh7a+mQqz2WmGGuY=
			</data>
		</dict>
		<key>ar.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WGWKsm0lr0i+xCHI3iI6qgGQUGc=
			</data>
			<key>hash2</key>
			<data>
			FjwIp4OocFcaztyeKyy0SO9mgZHcpMTIfgW1YX34Qg0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>az.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			I5I2sMxFN6VpkLlxo2awAMDkpQc=
			</data>
			<key>hash2</key>
			<data>
			feygIQzDUHXndRrZz+Y0K/OAsZ9hgoO6H6mnWKP1CrU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ca-ES.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			n1t9rsKSA/Qggp8b4YDmuYfgBUE=
			</data>
			<key>hash2</key>
			<data>
			7Jkzs7c/fAOMrYmnmjVzZqB1RwQ6JsNnqZ8JqM2m5/g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ca.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			atokjgvUP1Vmk9gdy1FVd/0SWOw=
			</data>
			<key>hash2</key>
			<data>
			klkvHLk/gxChDT4B8BXsPPUlIw1oE3mxR3rMx03H7CI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>cs.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7vpjV0K1NCsK1triQSGsKuyHQYo=
			</data>
			<key>hash2</key>
			<data>
			9ysy+FPxGFKWsk9ddoQO1+DEayo8jsYON/5VSfjtb5w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>da.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kdTabL7H2HnMKbiU1Ypr9YWn+SU=
			</data>
			<key>hash2</key>
			<data>
			6VGaLIIRBWOmNszgLK6sHwOe9m415DshnFEotjiu3j0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OmO6yBOPEjC40Ns1cyLfJqMHnRc=
			</data>
			<key>hash2</key>
			<data>
			WqIBZWBDqzaH0CgEnv1F8mXqO/JTRhtGFSCWS5xdSaA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			E7vzp81spzp/OVcH3QMTbzZd9m0=
			</data>
			<key>hash2</key>
			<data>
			yLFmnR9dU5TL2FWW7D/JpGVKZMQVQtc6A3riI7hmxN8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.stringsdict</key>
		<dict>
			<key>hash</key>
			<data>
			JT8QT5SebrK2XFFs3Iov80e9WkI=
			</data>
			<key>hash2</key>
			<data>
			Lr6rDy8EAt8S8WL0sGd9Nn5eViv3p1dMB5ttvTej0fk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			2SNBjq0z1PBctZSiG95PJo1r4rA=
			</data>
			<key>hash2</key>
			<data>
			bpE2MmYPfIMnFTTYY4w+oWphU0KjmfrEbc+FYDQRwus=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XvkNwTMy/L9Ul2FJuIFsdXb6tn0=
			</data>
			<key>hash2</key>
			<data>
			l19PYwhJCFKQjPVx+nFyA/mz3viDFl/7B3Ccok7CMKc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nb8AUSeVgi8iZZJtCZTM9Wnd1dY=
			</data>
			<key>hash2</key>
			<data>
			ZPSPYHFrtC/ahYHGxCtgzEx8hrvQt3US9nTdkAGWO98=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>hu.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hu6dFQI5ARjgSZLw1ijXAuO1hJU=
			</data>
			<key>hash2</key>
			<data>
			CCcezMSNsz/M/we2kN6SPgavOc9V3lKMtJ2F5CCDQtM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			b11yeT46Xu01huDf78GnDPTHFbQ=
			</data>
			<key>hash2</key>
			<data>
			AqJcoMQ/POWXK/I/clIUK1LccsSPO3Ypd1s9rUpbSJo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6H7j42CvSdaI1R1Zej6RWtwMkto=
			</data>
			<key>hash2</key>
			<data>
			X16eWDRm/QMzTLZCmbttUTkjOMcn+UcejKuLsrqgVHY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			I1wNK6mkWxnD8BPtLLbdQKyDyr0=
			</data>
			<key>hash2</key>
			<data>
			zzlGuiBkIxJDRMuXTnvbXI5BFY8SX+RthaovJhJpTf8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nb.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3nEYtISxR6bmu97Op52Os+biWYM=
			</data>
			<key>hash2</key>
			<data>
			nOL2y2CXIu1ZnAbti0UZQS7DHvjlfR6F/6uVIK5IhKY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9Ul1WgexQcR1U8skkMoEbV+qsTc=
			</data>
			<key>hash2</key>
			<data>
			vfF8XWGD93jA35fNWreTg625Ur5XySbicPCFyb2WDYw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/89hLMCQZUJU4t5D6d4omWX5cGA=
			</data>
			<key>hash2</key>
			<data>
			mH32Hs7STnDSe6V8x9g4pY1ZM9Y5jj1W/d2c1kIfG+k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-BR.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jPZtLy0k6hzRSC/2U3af6/kD1HE=
			</data>
			<key>hash2</key>
			<data>
			Cp+KO9ItQre7lQmcUmRut+LWrbJ3rYT6YraYni/sdjI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pt-PT.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vv2UhgZvnDlpsIXk/6Tm4jcTGE4=
			</data>
			<key>hash2</key>
			<data>
			T/yRtYvzKuNq0jyM4VXo3LjxM6smq1XSsSoHA2D/ycg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ro.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3DHKaLWL+U6mJr3ZtkrZsMckbMg=
			</data>
			<key>hash2</key>
			<data>
			rXC43x0KNU+p3u+3QA53DPyB9EyiXkmsX/7mFdcw2Bg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vPyLdPpxI/agU4aWWK8NDs9eb70=
			</data>
			<key>hash2</key>
			<data>
			k7pLL32V9InfZyGKxk83qaZeXG3423NQuUGs4Ans1l4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>sk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			JfWK424Bgf9OTJfyFNbMKu3/pGo=
			</data>
			<key>hash2</key>
			<data>
			1TuDZrwKOqiXBLkr4WE45ld1emhWHH0/QgToxC6jOR0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>strip-frameworks.sh</key>
		<dict>
			<key>hash</key>
			<data>
			YOS7aGxjvvRtsNAQCXrKoe5nT7w=
			</data>
			<key>hash2</key>
			<data>
			Lpm4UvZn1WmEqTctsiPIEzhoUTl6oMjiLvMOsfwL2As=
			</data>
		</dict>
		<key>sv.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			46AlVm227dP6G6m6puFihDwfcZ0=
			</data>
			<key>hash2</key>
			<data>
			OtMacaSHVWx0XXh3S84qB+Jv/oBpFs4amhr071rTPZ8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>tr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			leqXZMjnJYU4eKtP8ezzA3gDLqs=
			</data>
			<key>hash2</key>
			<data>
			/8K5JeZ8loJX7KDhES/nwsn08aDsorf2EQ1HfwkLQwE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			WBqtayk6qOeHjZtkPmpf7BQPMyQ=
			</data>
			<key>hash2</key>
			<data>
			HpKOrLNgAXQIDvVTx0qzg2dDv8MPMZekUMw22Bq2mns=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hant-TW.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vdr/3isGcsjy1OxQgNV5QWcDKNs=
			</data>
			<key>hash2</key>
			<data>
			HRUTak40GHI5pUm5GURzClJELyn/n4OcrqXwOPtrU2Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/4eBCWIJ9N0zXhm4ucKfovpyFUM=
			</data>
			<key>hash2</key>
			<data>
			CGKAgcEbel54Q9w6rVNKPq5Su4EZ1Bh4YSZ53lB/uvE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
