!function(){"use strict";let t,n,e,i,r={passive:!0,capture:!0},o=new Date,a=function(){i=[],n=-1,t=null,s(addEventListener)},u=function(i,r){t||(t=r,n=i,e=new Date,s(removeEventListener),c())},c=function(){if(n>=0&&n<e-o){let r={entryType:"first-input",name:t.type,target:t.target,cancelable:t.cancelable,startTime:t.timeStamp,processingStart:t.timeStamp+n};i.forEach(function(t){t(r)}),i=[]}},l=function(t){if(t.cancelable){var n,e;let i=(t.timeStamp>1e12?new Date:performance.now())-t.timeStamp,o,a,c;"pointerdown"==t.type?(n=i,e=t,o=function(){u(n,e),c()},a=function(){c()},c=function(){removeEventListener("pointerup",o,r),removeEventListener("pointercancel",a,r)},addEventListener("pointerup",o,r),addEventListener("pointercancel",a,r)):u(i,t)}},s=function(t){["mousedown","keydown","touchstart","pointerdown"].forEach(function(n){return t(n,l,r)})},f="hidden"===document.visibilityState?0:1/0;addEventListener("visibilitychange",function t(n){"hidden"===document.visibilityState&&(f=n.timeStamp,removeEventListener("visibilitychange",t,!0))},!0),a(),self.webVitals={firstInputPolyfill:function(t){i.push(t),c()},resetFirstInputPolyfill:a,get firstHiddenTime(){return f}}}(),function(t){"use strict";let n,e=-1,i=function(t){addEventListener("pageshow",function(n){n.persisted&&(e=n.timeStamp,t(n))},!0)},r=function(){return window.performance&&(performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]||function(){let t=performance.timing,i;for(let r in e={entryType:"navigation",startTime:0,type:i=2===(n=performance.navigation.type)?"back_forward":1===n?"reload":"navigate"},t)"navigationStart"!==r&&"toJSON"!==r&&(e[r]=Math.max(t[r]-t.navigationStart,0));return e}())},o=function(){let t=r();return t&&t.activationStart||0},a=function(t,n){let i=r(),a="navigate";return e>=0?a="back-forward-cache":i&&(document.prerendering||o()>0?a="prerender":document.wasDiscarded?a="restore":i.type&&(a=i.type.replace(/_/g,"-"))),{name:t,value:void 0===n?-1:n,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(crypto.getRandomValues(new Uint32Array(1))[0]),navigationType:a}},u=function(t,n,e){try{if(PerformanceObserver.supportedEntryTypes.includes(t)){let i=new PerformanceObserver(function(t){Promise.resolve().then(function(){n(t.getEntries())})});return i.observe(Object.assign({type:t,buffered:!0},e||{})),i}}catch(r){}},c=function(t,n,e,i){let r,o;return function(a){var u,c;n.value>=0&&(a||i)&&((o=n.value-(r||0))||void 0===r)&&(r=n.value,n.delta=o,n.rating=(u=n.value,u>(c=e)[1]?"poor":u>c[0]?"needs-improvement":"good"),t(n))}},l=function(t){requestAnimationFrame(function(){return requestAnimationFrame(function(){return t()})})},s=function(t){let n=function(n){"pagehide"!==n.type&&"hidden"!==document.visibilityState||t(n)};addEventListener("visibilitychange",n,!0),addEventListener("pagehide",n,!0)},f=function(t){let n=!1;return function(e){n||(t(e),n=!0)}},d=-1,$=function(t){"hidden"===document.visibilityState&&d>-1&&(d="visibilitychange"===t.type?t.timeStamp:0,h())},p=function(){addEventListener("visibilitychange",$,!0),addEventListener("prerenderingchange",$,!0)},h=function(){removeEventListener("visibilitychange",$,!0),removeEventListener("prerenderingchange",$,!0)},g=function(){return d<0&&((d=window.webVitals.firstHiddenTime)===1/0&&p(),setTimeout(function(){d="hidden"!==document.visibilityState||document.prerendering?1/0:0,p()},0)),{get firstHiddenTime(){return d}}},m=function(t){document.prerendering?addEventListener("prerenderingchange",function(){return t()},!0):t()},v=[1800,3e3],y=function(t,n){n=n||{},m(function(){let e,r=g(),s=a("FCP"),f=u("paint",function(t){t.forEach(function(t){"first-contentful-paint"===t.name&&(f.disconnect(),t.startTime<r.firstHiddenTime&&(s.value=Math.max(t.startTime-o(),0),s.entries.push(t),e(!0)))})});f&&(e=c(t,s,v,n.reportAllChanges),i(function(i){s=a("FCP"),e=c(t,s,v,n.reportAllChanges),l(function(){s.value=performance.now()-i.timeStamp,e(!0)})}))})},T=[.1,.25],b=function(t,n,e,i,r){return t.forEach(function(t){if(!t.hadRecentInput){let i=n[0],r=n[n.length-1];e&&t.startTime-r.startTime<1e3&&t.startTime-i.startTime<5e3?(e+=t.value,n.push(t)):(e=t.value,n=[t])}}),e>i.value&&(i.value=e,i.entries=n,r()),{o:e,d:n,r:i}},C=function(t,n){n=n||{},y(f(function(){var e,r,o;let f,d=a("CLS",0),$=0,p=[],h=function(t){let n=b(t,p,$,d,f);$=n.o,p=n.d,d=n.r},g=u("layout-shift",h);g&&(f=c(t,d,T,n.reportAllChanges),s((e=f,r=h,o=g,function(){r(o.takeRecords()),e(!0)})),i(function(){$=0,d=a("CLS",0),f=c(t,d,T,n.reportAllChanges),l(function(){return f()})}),setTimeout(f,0))}))},w=[100,300],S=function(t,n){n=n||{},m(function(){let e,r=g(),o=a("FID"),l=function(t){t.startTime<r.firstHiddenTime&&(o.value=t.processingStart-t.startTime,o.entries.push(t),e(!0))},d=function(t){t.forEach(l)},$=u("first-input",d);e=c(t,o,w,n.reportAllChanges),$?s(f(function(){d($.takeRecords()),$.disconnect()})):window.webVitals.firstInputPolyfill(l),i(function(){o=a("FID"),e=c(t,o,w,n.reportAllChanges),window.webVitals.resetFirstInputPolyfill(),window.webVitals.firstInputPolyfill(l)})})},_=0,I=1/0,P=0,F=function(t){t.forEach(function(t){t.interactionId&&(I=Math.min(I,t.interactionId),_=(P=Math.max(P,t.interactionId))?(P-I)/7+1:0)})},E=function(){return n?_:performance.interactionCount||0},A=function(){"interactionCount"in performance||n||(n=u("event",F,{type:"event",buffered:!0,durationThreshold:0}))},k=[200,500],L=0,B=function(){return E()-L},H=[],V={},D=function(t){let n=H[H.length-1],e=V[t.interactionId];if(e||H.length<10||t.duration>n.latency){if(e)e.entries.push(t),e.latency=Math.max(e.latency,t.duration);else{let i={id:t.interactionId,latency:t.duration,entries:[t]};V[i.id]=i,H.push(i)}H.sort(function(t,n){return n.latency-t.latency}),H.splice(10).forEach(function(t){delete V[t.id]})}},x=function(t){t.forEach(function(t){t.interactionId&&D(t),"first-input"!==t.entryType||H.some(function(n){return n.entries.some(function(n){return t.duration===n.duration&&t.startTime===n.startTime})})||D(t)})},N=function(t,n,e){return n&&n.latency!==t.value&&(t.value=n.latency,t.entries=n.entries,e()),t},R=function(t,n){n=n||{},m(function(){A();let e,r=a("INP"),o=function(t){x(t);let n,i=H[n=Math.min(H.length-1,Math.floor(B()/50))];r=N(r,i,e)},l=u("event",o,{durationThreshold:n.durationThreshold||40});e=c(t,r,k,n.reportAllChanges),l&&(l.observe({type:"first-input",buffered:!0}),s(function(){o(l.takeRecords()),r.value<0&&B()>0&&(r.value=0,r.entries=[]),e(!0)}),i(function(){H=[],L=E(),r=a("INP"),e=c(t,r,k,n.reportAllChanges)}))})},M=[2500,4e3],G={},J=function(t,n){n=n||{},m(function(){let e,r=g(),d=a("LCP"),$=function(t){let n=t[t.length-1];n&&n.startTime<r.firstHiddenTime&&(d.value=Math.max(n.startTime-o(),0),d.entries=[n],e())},p=u("largest-contentful-paint",$);if(p){e=c(t,d,M,n.reportAllChanges);let h=f(function(){G[d.id]||($(p.takeRecords()),p.disconnect(),G[d.id]=!0,e(!0))});["keydown","click"].forEach(function(t){addEventListener(t,h,!0)}),s(h),i(function(i){d=a("LCP"),e=c(t,d,M,n.reportAllChanges),l(function(){d.value=performance.now()-i.timeStamp,G[d.id]=!0,e(!0)})})}})},O=[800,1800],j=function t(n){document.prerendering?m(function(){return t(n)}):"complete"!==document.readyState?addEventListener("load",function(){return t(n)},!0):setTimeout(n,0)},q=function(t,n){n=n||{};let e=a("TTFB"),u=c(t,e,O,n.reportAllChanges);j(function(){let l=r();if(l){let s=l.responseStart;if(s<=0||s>performance.now())return;e.value=Math.max(s-o(),0),e.entries=[l],u(!0),i(function(){e=a("TTFB",0),(u=c(t,e,O,n.reportAllChanges))(!0)})}})};t.CLSThresholds=T,t.FCPThresholds=v,t.FIDThresholds=w,t.INPThresholds=k,t.LCPThresholds=M,t.TTFBThresholds=O,t.getCLS=C,t.getFCP=y,t.getFID=S,t.getINP=R,t.getLCP=J,t.ge_tFB=q,t.onCLS=C,t.onFCP=y,t.onFID=S,t.onINP=R,t.onLCP=J,t.onTTFB=q,Object.defineProperty(t,"__esModule",{value:!0})}(this.webVitals=this.webVitals||{}),webVitals.onFID(function(t){console.log("fidEntry:",t),window.webkit.messageHandlers.IBGMetricHandler.postMessage({type:"fid_value",value:t.value})});
