{"assets/device_assets/frame_lib.lua": ["assets/device_assets/frame_lib.lua"], "assets/fonts/SFPRODISPLAYBLACKITALIC.OTF": ["assets/fonts/SFPRODISPLAYBLACKITALIC.OTF"], "assets/fonts/SFPRODISPLAYBOLD.OTF": ["assets/fonts/SFPRODISPLAYBOLD.OTF"], "assets/fonts/SFPRODISPLAYHEAVYITALIC.OTF": ["assets/fonts/SFPRODISPLAYHEAVYITALIC.OTF"], "assets/fonts/SFPRODISPLAYLIGHTITALIC.OTF": ["assets/fonts/SFPRODISPLAYLIGHTITALIC.OTF"], "assets/fonts/SFPRODISPLAYMEDIUM.OTF": ["assets/fonts/SFPRODISPLAYMEDIUM.OTF"], "assets/fonts/SFPRODISPLAYREGULAR.OTF": ["assets/fonts/SFPRODISPLAYREGULAR.OTF"], "assets/fonts/SFPRODISPLAYSEMIBOLDITALIC.OTF": ["assets/fonts/SFPRODISPLAYSEMIBOLDITALIC.OTF"], "assets/fonts/SFPRODISPLAYTHINITALIC.OTF": ["assets/fonts/SFPRODISPLAYTHINITALIC.OTF"], "assets/images/1.mov": ["assets/images/1.mov"], "assets/images/2.mov": ["assets/images/2.mov"], "assets/images/3.mov": ["assets/images/3.mov"], "assets/images/4.mov": ["assets/images/4.mov"], "assets/images/5.mov": ["assets/images/5.mov"], "assets/images/Logo Text White.png": ["assets/images/Logo Text White.png"], "assets/images/ai_magic.svg": ["assets/images/ai_magic.svg"], "assets/images/app_launcher_icon.png": ["assets/images/app_launcher_icon.png"], "assets/images/app_launcher_icon_old.png": ["assets/images/app_launcher_icon_old.png"], "assets/images/app_launcher_icon_v1.png": ["assets/images/app_launcher_icon_v1.png"], "assets/images/app_launcher_icon_v2.png": ["assets/images/app_launcher_icon_v2.png"], "assets/images/apple-reminders-logo.png": ["assets/images/apple-reminders-logo.png"], "assets/images/apple_logo.png": ["assets/images/apple_logo.png"], "assets/images/background.png": ["assets/images/background.png"], "assets/images/blob.png": ["assets/images/blob.png"], "assets/images/calendar_logo.png": ["assets/images/calendar_logo.png"], "assets/images/checkbox.svg": ["assets/images/checkbox.svg"], "assets/images/clone.png": ["assets/images/clone.png"], "assets/images/email_logo.png": ["assets/images/email_logo.png"], "assets/images/emotional_feedback_1.png": ["assets/images/emotional_feedback_1.png"], "assets/images/facebook_logo.png": ["assets/images/facebook_logo.png"], "assets/images/google_logo.png": ["assets/images/google_logo.png"], "assets/images/gradient_card.png": ["assets/images/gradient_card.png"], "assets/images/herologo.png": ["assets/images/herologo.png"], "assets/images/herologo_v1.png": ["assets/images/herologo_v1.png"], "assets/images/herologo_v3.png": ["assets/images/herologo_v3.png"], "assets/images/herologo_v4.png": ["assets/images/herologo_v4.png"], "assets/images/ic_chart.svg": ["assets/images/ic_chart.svg"], "assets/images/ic_clone_chat.svg": ["assets/images/ic_clone_chat.svg"], "assets/images/ic_clone_plus.svg": ["assets/images/ic_clone_plus.svg"], "assets/images/ic_dollar.svg": ["assets/images/ic_dollar.svg"], "assets/images/ic_persona_profile.svg": ["assets/images/ic_persona_profile.svg"], "assets/images/ic_setting_persona.svg": ["assets/images/ic_setting_persona.svg"], "assets/images/imessage_logo.svg": ["assets/images/imessage_logo.svg"], "assets/images/instagram_logo.png": ["assets/images/instagram_logo.png"], "assets/images/instruction_1.png": ["assets/images/instruction_1.png"], "assets/images/instruction_2.png": ["assets/images/instruction_2.png"], "assets/images/instruction_3.png": ["assets/images/instruction_3.png"], "assets/images/link_icon.svg": ["assets/images/link_icon.svg"], "assets/images/linkedin_logo.png": ["assets/images/linkedin_logo.png"], "assets/images/logo_transparent.png": ["assets/images/logo_transparent.png"], "assets/images/logo_transparent_v1.png": ["assets/images/logo_transparent_v1.png"], "assets/images/logo_transparent_v2.png": ["assets/images/logo_transparent_v2.png"], "assets/images/new_background.png": ["assets/images/new_background.png"], "assets/images/notion_logo.png": ["assets/images/notion_logo.png"], "assets/images/omi-devkit-without-rope.png": ["assets/images/omi-devkit-without-rope.png"], "assets/images/omi-glass.png": ["assets/images/omi-glass.png"], "assets/images/omi-without-rope-turned-off.png": ["assets/images/omi-without-rope-turned-off.png"], "assets/images/omi-without-rope.png": ["assets/images/omi-without-rope.png"], "assets/images/onboarding-bg-1.jpg": ["assets/images/onboarding-bg-1.jpg"], "assets/images/onboarding-bg-2.jpg": ["assets/images/onboarding-bg-2.jpg"], "assets/images/onboarding-bg-3.jpg": ["assets/images/onboarding-bg-3.jpg"], "assets/images/onboarding-bg-4.jpg": ["assets/images/onboarding-bg-4.jpg"], "assets/images/onboarding-bg-5-1.jpg": ["assets/images/onboarding-bg-5-1.jpg"], "assets/images/onboarding-bg-5-2.jpg": ["assets/images/onboarding-bg-5-2.jpg"], "assets/images/onboarding-bg-6.jpg": ["assets/images/onboarding-bg-6.jpg"], "assets/images/onboarding-language-grey.png": ["assets/images/onboarding-language-grey.png"], "assets/images/onboarding-name-grey.png": ["assets/images/onboarding-name-grey.png"], "assets/images/onboarding-name-white.png": ["assets/images/onboarding-name-white.png"], "assets/images/onboarding-name.png": ["assets/images/onboarding-name.png"], "assets/images/onboarding-permissions.png": ["assets/images/onboarding-permissions.png"], "assets/images/onboarding.mp4": ["assets/images/onboarding.mp4"], "assets/images/recording_green_circle_icon.png": ["assets/images/recording_green_circle_icon.png"], "assets/images/slack_logo.png": ["assets/images/slack_logo.png"], "assets/images/speaker_0_icon.png": ["assets/images/speaker_0_icon.png"], "assets/images/speaker_1_icon.png": ["assets/images/speaker_1_icon.png"], "assets/images/splash.png": ["assets/images/splash.png"], "assets/images/splash_icon.png": ["assets/images/splash_icon.png"], "assets/images/splash_icon_v1.png": ["assets/images/splash_icon_v1.png"], "assets/images/splash_icon_v2.png": ["assets/images/splash_icon_v2.png"], "assets/images/splash_v1.png": ["assets/images/splash_v1.png"], "assets/images/splash_v2.png": ["assets/images/splash_v2.png"], "assets/images/stars.png": ["assets/images/stars.png"], "assets/images/stripe_logo.svg": ["assets/images/stripe_logo.svg"], "assets/images/telegram_logo.png": ["assets/images/telegram_logo.png"], "assets/images/whatsapp_logo.png": ["assets/images/whatsapp_logo.png"], "assets/images/x_logo.png": ["assets/images/x_logo.png"], "assets/images/x_logo_mini.png": ["assets/images/x_logo_mini.png"], "assets/images/youtube_logo.png": ["assets/images/youtube_logo.png"], "assets/lottie_animations/no_internet.json": ["assets/lottie_animations/no_internet.json"], "assets/lottie_animations/server_error.json": ["assets/lottie_animations/server_error.json"], "assets/lottie_animations/wave.json": ["assets/lottie_animations/wave.json"], "assets/pdfs/favicon.png": ["assets/pdfs/favicon.png"], "assets/silero_vad.onnx": ["assets/silero_vad.onnx"], "assets/silero_vad.v5.onnx": ["assets/silero_vad.v5.onnx"], "packages/awesome_notifications/test/assets/images/test_image.png": ["packages/awesome_notifications/test/assets/images/test_image.png"], "packages/flutter_sound/assets/js/async_processor.js": ["packages/flutter_sound/assets/js/async_processor.js"], "packages/flutter_sound/assets/js/tau_web.js": ["packages/flutter_sound/assets/js/tau_web.js"], "packages/flutter_sound_web/howler/howler.js": ["packages/flutter_sound_web/howler/howler.js"], "packages/flutter_sound_web/src/flutter_sound.js": ["packages/flutter_sound_web/src/flutter_sound.js"], "packages/flutter_sound_web/src/flutter_sound_player.js": ["packages/flutter_sound_web/src/flutter_sound_player.js"], "packages/flutter_sound_web/src/flutter_sound_recorder.js": ["packages/flutter_sound_web/src/flutter_sound_recorder.js"], "packages/flutter_sound_web/src/flutter_sound_stream_processor.js": ["packages/flutter_sound_web/src/flutter_sound_stream_processor.js"], "packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf": ["packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf"], "packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf": ["packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf"], "packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf": ["packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf"], "packages/frame_sdk/assets/frameLib.lua": ["packages/frame_sdk/assets/frameLib.lua"], "packages/google_sign_in_all_platforms_desktop/assets/post_auth_page.html": ["packages/google_sign_in_all_platforms_desktop/assets/post_auth_page.html"], "packages/map_launcher/assets/icons/amap.svg": ["packages/map_launcher/assets/icons/amap.svg"], "packages/map_launcher/assets/icons/apple.svg": ["packages/map_launcher/assets/icons/apple.svg"], "packages/map_launcher/assets/icons/baidu.svg": ["packages/map_launcher/assets/icons/baidu.svg"], "packages/map_launcher/assets/icons/citymapper.svg": ["packages/map_launcher/assets/icons/citymapper.svg"], "packages/map_launcher/assets/icons/copilot.svg": ["packages/map_launcher/assets/icons/copilot.svg"], "packages/map_launcher/assets/icons/doubleGis.svg": ["packages/map_launcher/assets/icons/doubleGis.svg"], "packages/map_launcher/assets/icons/flitsmeister.svg": ["packages/map_launcher/assets/icons/flitsmeister.svg"], "packages/map_launcher/assets/icons/google.svg": ["packages/map_launcher/assets/icons/google.svg"], "packages/map_launcher/assets/icons/googleGo.svg": ["packages/map_launcher/assets/icons/googleGo.svg"], "packages/map_launcher/assets/icons/here.svg": ["packages/map_launcher/assets/icons/here.svg"], "packages/map_launcher/assets/icons/kakao.svg": ["packages/map_launcher/assets/icons/kakao.svg"], "packages/map_launcher/assets/icons/mappls.svg": ["packages/map_launcher/assets/icons/mappls.svg"], "packages/map_launcher/assets/icons/mapswithme.svg": ["packages/map_launcher/assets/icons/mapswithme.svg"], "packages/map_launcher/assets/icons/mapyCz.svg": ["packages/map_launcher/assets/icons/mapyCz.svg"], "packages/map_launcher/assets/icons/naver.svg": ["packages/map_launcher/assets/icons/naver.svg"], "packages/map_launcher/assets/icons/osmand.svg": ["packages/map_launcher/assets/icons/osmand.svg"], "packages/map_launcher/assets/icons/osmandplus.svg": ["packages/map_launcher/assets/icons/osmandplus.svg"], "packages/map_launcher/assets/icons/petal.svg": ["packages/map_launcher/assets/icons/petal.svg"], "packages/map_launcher/assets/icons/sygicTruck.svg": ["packages/map_launcher/assets/icons/sygicTruck.svg"], "packages/map_launcher/assets/icons/tencent.svg": ["packages/map_launcher/assets/icons/tencent.svg"], "packages/map_launcher/assets/icons/tmap.svg": ["packages/map_launcher/assets/icons/tmap.svg"], "packages/map_launcher/assets/icons/tomtomgo.svg": ["packages/map_launcher/assets/icons/tomtomgo.svg"], "packages/map_launcher/assets/icons/tomtomgofleet.svg": ["packages/map_launcher/assets/icons/tomtomgofleet.svg"], "packages/map_launcher/assets/icons/truckmeister.svg": ["packages/map_launcher/assets/icons/truckmeister.svg"], "packages/map_launcher/assets/icons/waze.svg": ["packages/map_launcher/assets/icons/waze.svg"], "packages/map_launcher/assets/icons/yandexMaps.svg": ["packages/map_launcher/assets/icons/yandexMaps.svg"], "packages/map_launcher/assets/icons/yandexNavi.svg": ["packages/map_launcher/assets/icons/yandexNavi.svg"], "packages/mcumgr_flutter/assets/mock_logs.txt": ["packages/mcumgr_flutter/assets/mock_logs.txt"], "packages/mixpanel_flutter/assets/mixpanel.js": ["packages/mixpanel_flutter/assets/mixpanel.js"], "packages/opus_flutter_web/assets/libopus.js": ["packages/opus_flutter_web/assets/libopus.js"], "packages/opus_flutter_web/assets/libopus.wasm": ["packages/opus_flutter_web/assets/libopus.wasm"], "packages/opus_flutter_windows/assets/libopus_x64.dll.blob": ["packages/opus_flutter_windows/assets/libopus_x64.dll.blob"], "packages/opus_flutter_windows/assets/libopus_x86.dll.blob": ["packages/opus_flutter_windows/assets/libopus_x86.dll.blob"], "packages/opus_flutter_windows/assets/opus_license.txt": ["packages/opus_flutter_windows/assets/opus_license.txt"], "packages/win_ble/assets/BLEServer.exe": ["packages/win_ble/assets/BLEServer.exe"], "packages/window_manager/images/ic_chrome_close.png": ["packages/window_manager/images/ic_chrome_close.png"], "packages/window_manager/images/ic_chrome_maximize.png": ["packages/window_manager/images/ic_chrome_maximize.png"], "packages/window_manager/images/ic_chrome_minimize.png": ["packages/window_manager/images/ic_chrome_minimize.png"], "packages/window_manager/images/ic_chrome_unmaximize.png": ["packages/window_manager/images/ic_chrome_unmaximize.png"], "shorebird.yaml": ["shorebird.yaml"]}